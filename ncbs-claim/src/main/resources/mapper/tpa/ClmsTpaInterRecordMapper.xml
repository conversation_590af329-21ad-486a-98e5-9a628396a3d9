<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.tpa.ClmsTpaInterRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaInterRecord">
        <id column="id" property="id" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="product" property="product" />
        <result column="scheme" property="scheme" />
        <result column="process" property="process" />
        <result column="submitter" property="submitter" />
        <result column="report_time" property="reportTime" />
        <result column="business_no" property="businessNo" />
        <result column="request_type" property="requestType" />
        <result column="request_info" property="requestInfo" />
        <result column="response_info" property="responseInfo" />
        <result column="is_success" property="isSuccess" />
        <result column="remark" property="remark" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
    </resultMap>

    <!-- 插入操作 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaInterRecord">
        INSERT INTO clms_tpa_inter_record (
        id,
        report_no,
        case_times,
        product,
        scheme,
        process,
        submitter,
        report_time,
        business_no,
        request_type,
        request_info,
        response_info,
        is_success,
        remark,
        created_by,
        sys_ctime,
        updated_by,
        sys_utime
        ) VALUES (
        #{id,jdbcType=VARCHAR},
        #{reportNo,jdbcType=VARCHAR},
        #{caseTimes,jdbcType=INTEGER},
        #{product,jdbcType=VARCHAR},
        #{scheme,jdbcType=VARCHAR},
        #{process,jdbcType=VARCHAR},
        #{submitter,jdbcType=VARCHAR},
        #{reportTime,jdbcType=TIMESTAMP},
        #{businessNo,jdbcType=VARCHAR},
        #{requestType,jdbcType=VARCHAR},
        #{requestInfo,jdbcType=LONGVARCHAR},
        #{responseInfo,jdbcType=LONGVARCHAR},
        #{isSuccess,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{createdBy,jdbcType=VARCHAR},
        #{sysCtime,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR},
        #{sysUtime,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 根据ID更新操作 -->
    <update id="updateById" parameterType="com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaInterRecord">
        UPDATE clms_tpa_inter_record
        SET
        report_no = #{reportNo,jdbcType=VARCHAR},
        case_times = #{caseTimes,jdbcType=INTEGER},
        product = #{product,jdbcType=VARCHAR},
        scheme = #{scheme,jdbcType=VARCHAR},
        process = #{process,jdbcType=VARCHAR},
        submitter = #{submitter,jdbcType=VARCHAR},
        report_time = #{reportTime,jdbcType=TIMESTAMP},
        business_no = #{businessNo,jdbcType=VARCHAR},
        request_type = #{requestType,jdbcType=VARCHAR},
        request_info = #{requestInfo,jdbcType=LONGVARCHAR},
        response_info = #{responseInfo,jdbcType=LONGVARCHAR},
        is_success = #{isSuccess,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        updated_by = #{updatedBy,jdbcType=VARCHAR},
        sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>

    <!-- 根据ID删除操作 -->
    <delete id="deleteById" parameterType="java.lang.String">
        DELETE FROM clms_tpa_inter_record
        WHERE id = #{id,jdbcType=VARCHAR}
    </delete>

    <!-- 根据ID查询操作 -->
    <select id="selectById" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        id,
        report_no,
        case_times,
        product,
        scheme,
        process,
        submitter,
        report_time,
        business_no,
        request_type,
        request_info,
        response_info,
        is_success,
        remark,
        created_by,
        sys_ctime,
        updated_by,
        sys_utime
        FROM clms_tpa_inter_record
        WHERE id = #{id,jdbcType=VARCHAR}
    </select>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        id,
        report_no,
        case_times,
        product,
        scheme,
        process,
        submitter,
        report_time,
        business_no,
        request_type,
        request_info,
        response_info,
        is_success,
        remark,
        created_by,
        sys_ctime,
        updated_by,
        sys_utime
        FROM clms_tpa_inter_record
        ORDER BY sys_utime DESC
    </select>

</mapper>
