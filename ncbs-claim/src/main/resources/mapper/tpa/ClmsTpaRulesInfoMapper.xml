<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.tpa.ClmsTpaRulesInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaRulesInfo">
        <id column="id" property="id"/>
        <result column="task_bpm_key" property="taskBpmKey"/>
        <result column="opt_type" property="optType"/>
        <result column="is_reopened" property="isReopened"/>
        <result column="submitter" property="submitter"/>
        <result column="error_type" property="errorType"/>
        <result column="reopened_type" property="reopenedType"/>
        <result column="is_tpa_consistent" property="isTpaConsistent"/>
        <result column="is_valid" property="isValid"/>
        <result column="invalid_reason" property="invalidReason"/>
        <result column="remark" property="remark"/>
        <result column="created_by" property="createdBy"/>
        <result column="sys_ctime" property="sysCtime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="sys_utime" property="sysUtime"/>
    </resultMap>
    <!-- 插入操作 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaRulesInfo">
        INSERT INTO clms_tpa_rules_info (
        id,
        task_bpm_key,
        opt_type,
        is_reopened,
        submitter,
        error_type,
        reopened_type,
        is_tpa_consistent,
        is_valid,
        invalid_reason,
        remark,
        created_by,
        sys_ctime,
        updated_by,
        sys_utime
        ) VALUES (
        #{id,jdbcType=VARCHAR},
        #{taskBpmKey,jdbcType=VARCHAR},
        #{optType,jdbcType=VARCHAR},
        #{isReopened,jdbcType=VARCHAR},
        #{submitter,jdbcType=VARCHAR},
        #{errorType,jdbcType=VARCHAR},
        #{reopenedType,jdbcType=VARCHAR},
        #{isTpaConsistent,jdbcType=VARCHAR},
        #{isValid,jdbcType=VARCHAR},
        #{invalidReason,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{createdBy,jdbcType=VARCHAR},
        #{sysCtime,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR},
        #{sysUtime,jdbcType=TIMESTAMP}
        )
    </insert>

    <!-- 根据ID更新操作 -->
    <update id="updateById" parameterType="com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaRulesInfo">
        UPDATE clms_tpa_rules_info
        SET
        task_bpm_key = #{taskBpmKey,jdbcType=VARCHAR},
        opt_type = #{optType,jdbcType=VARCHAR},
        is_reopened = #{isReopened,jdbcType=VARCHAR},
        submitter = #{submitter,jdbcType=VARCHAR},
        error_type = #{errorType,jdbcType=VARCHAR},
        reopened_type = #{reopenedType,jdbcType=VARCHAR},
        is_tpa_consistent = #{isTpaConsistent,jdbcType=VARCHAR},
        is_valid = #{isValid,jdbcType=VARCHAR},
        invalid_reason = #{invalidReason,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        updated_by = #{updatedBy,jdbcType=VARCHAR},
        sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>

    <!-- 根据ID删除操作 -->
    <delete id="deleteById" parameterType="java.lang.String">
        DELETE FROM clms_tpa_rules_info
        WHERE id = #{id,jdbcType=VARCHAR}
    </delete>

    <!-- 根据ID查询操作 -->
    <select id="selectById" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        id,
        task_bpm_key,
        opt_type,
        is_reopened,
        submitter,
        error_type,
        reopened_type,
        is_tpa_consistent,
        is_valid,
        invalid_reason,
        remark,
        created_by,
        sys_ctime,
        updated_by,
        sys_utime
        FROM clms_tpa_rules_info
        WHERE id = #{id,jdbcType=VARCHAR}
    </select>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        id,
        task_bpm_key,
        opt_type,
        is_reopened,
        submitter,
        error_type,
        reopened_type,
        is_tpa_consistent,
        is_valid,
        invalid_reason,
        remark,
        created_by,
        sys_ctime,
        updated_by,
        sys_utime
        FROM clms_tpa_rules_info
        ORDER BY sys_utime DESC
    </select>
    <!-- 查询所有记录 -->
    <select id="getTpaRules" parameterType="java.util.Map" resultMap="BaseResultMap">
        select * from clms_tpa_rules_info
        where is_valid='1'
        <if test="taskBpmKey != null">
            and task_bpm_key = #{taskBpmKey,jdbcType=VARCHAR}
        </if>
    </select>
</mapper>
