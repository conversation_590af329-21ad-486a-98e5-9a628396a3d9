<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.paic.ncbs.claim.dao.mapper.tpa.ClmsTpaSettleInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaSettleInfo">
        <id column="id" property="id" />
        <result column="business_no" property="businessNo" />
        <result column="report_no" property="reportNo" />
        <result column="case_times" property="caseTimes" />
        <result column="task_bpm_key" property="taskBpmKey" />
        <result column="opt_type" property="optType" />
        <result column="report_time" property="reportTime" />
        <result column="policy_no" property="policyNo" />
        <result column="submitter" property="submitter" />
        <result column="submit_tpa" property="submitTpa" />
        <result column="service_no" property="serviceNo" />
        <result column="amount" property="amount" />
        <result column="end_case_date" property="endCaseDate" />
        <result column="settle_flag" property="settleFlag" />
        <result column="is_valid" property="isValid" />
        <result column="invalid_reason" property="invalidReason" />
        <result column="remark" property="remark" />
        <result column="created_by" property="createdBy" />
        <result column="sys_ctime" property="sysCtime" />
        <result column="updated_by" property="updatedBy" />
        <result column="sys_utime" property="sysUtime" />
        <result column="billing_method" property="billingMethod" />
        <result column="tax_rate" property="taxRate" />
        <result column="is_people_hurt" property="isPeopleHurt" />
        <result column="server_name" property="serverName" />
        <result column="server_details" property="serverDetails" />
        <result column="billing_mode" property="billingMode" />
        <result column="communicate_title" property="communicateTitle" />
        <result column="valid_period" property="validPeriod" />
        <result column="apply_all_products" property="applyAllProducts" />
    </resultMap>

    <!-- 作业情况查询映射结果 -->
    <resultMap id="HomeworkResultMap" type="com.paic.ncbs.claim.model.vo.tpa.TpaHomeworkStatusVo">
        <result column="report_case_number" property="reportCaseNumber" />
        <result column="case_number" property="caseNumber" />
        <result column="reopen_number" property="reopenNumber" />
        <result column="average_pay" property="averagePay" />
        <result column="filing_cycle" property="filingCycle" />
        <result column="case_cycle" property="caseCycle" />
        <result column="case_rate" property="caseRate" />
        <result column="tpa_pay" property="tpaPay" />
        <result column="errors_number" property="errorsNumber" />
    </resultMap>

    <!-- 未决情况查询映射结果 -->
    <resultMap id="PendingInfoResultMap" type="com.paic.ncbs.claim.model.vo.tpa.TpaPendingInfoVo">
        <result column="pendingCaseCount" property="pendingCaseCount" />
        <result column="disabilityPendingCount" property="disabilityPendingCount" />
        <result column="deathPendingCount" property="deathPendingCount" />
        <result column="avgUndeterminedAmount" property="avgUndeterminedAmount" />
        <result column="avgPendingDays" property="avgPendingDays" />
    </resultMap>

    <!-- 插入操作 -->
    <insert id="insert" parameterType="com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaSettleInfo">
        INSERT INTO clms_tpa_settle_info (
        id,
        business_no,
        report_no,
        case_times,
        task_bpm_key,
        opt_type,
        report_time,
        policy_no,
        submitter,
        submit_tpa,
        service_no,
        amount,
        end_case_date,
        settle_flag,
        is_valid,
        invalid_reason,
        remark,
        created_by,
        sys_ctime,
        updated_by,
        sys_utime
        ) VALUES (
        #{id,jdbcType=VARCHAR},
        #{businessNo,jdbcType=VARCHAR},
        #{reportNo,jdbcType=VARCHAR},
        #{caseTimes,jdbcType=INTEGER},
        #{taskBpmKey,jdbcType=VARCHAR},
        #{optType,jdbcType=VARCHAR},
        #{reportTime,jdbcType=TIMESTAMP},
        #{policyNo,jdbcType=VARCHAR},
        #{submitter,jdbcType=VARCHAR},
        #{submitTpa,jdbcType=VARCHAR},
        #{serviceNo,jdbcType=VARCHAR},
        #{amount,jdbcType=DECIMAL},
        #{endCaseDate,jdbcType=TIMESTAMP},
        #{settleFlag,jdbcType=VARCHAR},
        #{isValid,jdbcType=VARCHAR},
        #{invalidReason,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{createdBy,jdbcType=VARCHAR},
        #{sysCtime,jdbcType=TIMESTAMP},
        #{updatedBy,jdbcType=VARCHAR},
        #{sysUtime,jdbcType=TIMESTAMP}
        )
    </insert>

    <insert id="saveSettleInfo" parameterType="java.util.List">
        INSERT INTO clms_tpa_settle_info (
        id,
        business_no,
        report_no,
        case_times,
        task_bpm_key,
        opt_type,
        report_time,
        policy_no,
        submitter,
        submit_tpa,
        service_no,
        amount,
        end_case_date,
        settle_flag,
        is_valid,
        invalid_reason,
        remark,
        created_by,
        sys_ctime,
        updated_by,
        sys_utime,
        billing_method,
        tax_rate,
        is_people_hurt,
        server_name,
        server_details,
        billing_mode,
        communicate_title,
        valid_period,
        apply_all_products)
        VALUES
        <foreach collection="settleInfoList" item="item" index="index" separator="," >
            ( #{item.id,jdbcType=VARCHAR},
            #{item.businessNo,jdbcType=VARCHAR},
            #{item.reportNo,jdbcType=VARCHAR},
            #{item.caseTimes,jdbcType=INTEGER},
            #{item.taskBpmKey,jdbcType=VARCHAR},
            #{item.optType,jdbcType=VARCHAR},
            #{item.reportTime,jdbcType=TIMESTAMP},
            #{item.policyNo,jdbcType=VARCHAR},
            #{item.submitter,jdbcType=VARCHAR},
            #{item.submitTpa,jdbcType=VARCHAR},
            #{item.serviceNo,jdbcType=VARCHAR},
            #{item.amount,jdbcType=DECIMAL},
            #{item.endCaseDate,jdbcType=TIMESTAMP},
            #{item.settleFlag,jdbcType=VARCHAR},
            #{item.isValid,jdbcType=VARCHAR},
            #{item.invalidReason,jdbcType=VARCHAR},
            #{item.remark,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.sysCtime,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR},
            #{item.sysUtime,jdbcType=TIMESTAMP},
            #{item.billingMethod,jdbcType=TIMESTAMP},
            #{item.taxRate,jdbcType=DECIMAL},
            #{item.isPeopleHurt,jdbcType=TIMESTAMP},
            #{item.serverName,jdbcType=TIMESTAMP},
            #{item.serverDetails,jdbcType=TIMESTAMP},
            #{item.billingMode,jdbcType=VARCHAR},
            #{item.communicateTitle,jdbcType=VARCHAR},
            #{item.validPeriod,jdbcType=VARCHAR},
            #{item.applyAllProducts,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 根据ID更新操作 -->
    <update id="updateById" parameterType="com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaSettleInfo">
        UPDATE clms_tpa_settle_info
        SET
        business_no = #{businessNo,jdbcType=VARCHAR},
        report_no = #{reportNo,jdbcType=VARCHAR},
        case_times = #{caseTimes,jdbcType=INTEGER},
        task_bpm_key = #{taskBpmKey,jdbcType=VARCHAR},
        opt_type = #{optType,jdbcType=VARCHAR},
        report_time = #{reportTime,jdbcType=TIMESTAMP},
        policy_no = #{policyNo,jdbcType=VARCHAR},
        submitter = #{submitter,jdbcType=VARCHAR},
        submit_tpa = #{submitTpa,jdbcType=VARCHAR},
        service_no = #{serviceNo,jdbcType=VARCHAR},
        amount = #{amount,jdbcType=DECIMAL},
        end_case_date = #{endCaseDate,jdbcType=TIMESTAMP},
        settle_flag = #{settleFlag,jdbcType=VARCHAR},
        is_valid = #{isValid,jdbcType=VARCHAR},
        invalid_reason = #{invalidReason,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        updated_by = #{updatedBy,jdbcType=VARCHAR},
        sys_utime = #{sysUtime,jdbcType=TIMESTAMP}
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 根据报案号+赔付次数+保单号+环节号+环节操作类型修改成无效操作 -->
    <update id="delSettleInfo" parameterType="com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaSettleInfo">
        UPDATE clms_tpa_settle_info
        SET is_valid = '0',invalid_reason = #{invalidReason,jdbcType=VARCHAR}
        WHERE report_no = #{reportNo,jdbcType=VARCHAR}
        and case_times = #{caseTimes,jdbcType=INTEGER}
        and task_bpm_key = #{taskBpmKey,jdbcType=VARCHAR}
        and opt_type = #{optType,jdbcType=VARCHAR}
        and is_valid = '1'
        <if test="submitter != null">
            and submitter = #{submitter,jdbcType=VARCHAR}
            order by sys_ctime desc limit 1
        </if>

    </update>

    <!-- 根据报案号+赔付次数修改结案时间操作 -->
    <update id="updateSettleInfo" parameterType="com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaSettleInfo">
        UPDATE clms_tpa_settle_info
        SET end_case_date = #{endCaseDate,jdbcType=TIMESTAMP}
        WHERE report_no = #{reportNo,jdbcType=VARCHAR}
        and case_times = #{caseTimes,jdbcType=INTEGER}
        and is_valid = '1'

    </update>

    <!-- 根据报案号+赔付次数修改结算状态操作 -->
    <update id="updateSettleStatus" parameterType="com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaSettleInfo">
        UPDATE clms_tpa_settle_info
        SET settle_flag = #{settleFlag,jdbcType=VARCHAR}
        WHERE report_no = #{reportNo,jdbcType=VARCHAR}
        and case_times = #{caseTimes,jdbcType=INTEGER}
        and is_valid = '1'

    </update>

    <!-- 根据ID删除操作 -->
    <delete id="deleteById" parameterType="java.lang.String">
        DELETE FROM clms_tpa_settle_info
        WHERE id = #{id,jdbcType=VARCHAR}
    </delete>

    <!-- 根据ID查询操作 -->
    <select id="selectById" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        *
        FROM clms_tpa_settle_info
        WHERE id = #{id,jdbcType=VARCHAR}
    </select>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        *
        FROM clms_tpa_settle_info
        ORDER BY sys_utime DESC
    </select>

    <!-- 根据任务环节号+报案号+赔付次数查询记录 -->
    <select id="selectTpaSettleInfo" parameterType="java.util.Map" resultMap="BaseResultMap">
        select * from clms_tpa_settle_info
        where 1=1
        <if test="isValid != null">
            and is_valid = #{isValid,jdbcType=VARCHAR}
        </if>
        <if test="reportNo != null">
            and report_no = #{reportNo,jdbcType=VARCHAR}
        </if>
        <if test="caseTimes != null">
            and case_times = #{caseTimes,jdbcType=INTEGER}
        </if>
        <if test="taskBpmKey != null">
            and task_bpm_key = #{taskBpmKey,jdbcType=VARCHAR}
        </if>
        <if test="optType != null">
            and opt_type = #{optType,jdbcType=VARCHAR}
        </if>
        <if test="settleFlag != null">
            and settle_flag = #{settleFlag,jdbcType=VARCHAR}
        </if>
        <if test="isPeopleHurt != null">
            and is_people_hurt = #{isPeopleHurt,jdbcType=VARCHAR}
        </if>

    </select>



    <!-- TPA看板-作业详情 -->
    <select id="getTpaHomeworkStatus" parameterType="com.paic.ncbs.claim.model.vo.tpa.TpaHomeworkStatusRequestVo"
            resultMap="HomeworkResultMap">
        WITH common_data AS (
        SELECT
        a.*,
        b.DEPARTMENT_CODE AS case_department_code,
        c.REPORT_DATE,
        d.product_code,
        e.CREATED_DATE as approval_reopen_date,
        f.POLICY_PAY,
        g.AUDIT_DATE
        FROM clms_tpa_settle_info a
        LEFT JOIN clm_case_base b
        ON a.report_no = b.report_no
        AND a.case_times = b.case_times
        LEFT JOIN CLM_REPORT_INFO c
        ON a.report_no = c.report_no
        LEFT JOIN clms_policy_info d
        ON a.report_no = d.report_no
        LEFT JOIN clm_restart_case_record e
        ON a.report_no = e.report_no
        AND a.case_times = e.case_times
        AND e.APPROVAL_OPINIONS='0'
        LEFT JOIN clm_policy_pay f
        ON a.report_no = f.report_no
        AND a.case_times = f.case_times
        AND f.POLICY_PAY IS NOT NULL
        LEFT JOIN clms_case_register_apply g
        ON a.report_no = g.report_no
        AND a.case_times = g.case_times
        AND g.AUDIT_OPINION = '1'
        WHERE a.is_valid = '1'
        <if test="departmentCodes != null and departmentCodes != ''">
            AND b.DEPARTMENT_CODE IN
            <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test='productCode != null and productCode != "1"'>
            AND d.productCode = #{productCode}
        </if>
        ),
        report_case_stats AS (
        SELECT
        COUNT(*) as report_case_number
        FROM common_data aa
        WHERE task_bpm_key='01'
        <choose>
            <when test='statisticsDate == "day"'>
                AND DATE(REPORT_DATE) = CURDATE()
            </when>
            <when test='statisticsDate == "week"'>
                AND YEAR(REPORT_DATE) = YEAR(CURDATE())
                AND WEEK(REPORT_DATE, 1) = WEEK(CURDATE(), 1)
            </when>
            <when test='statisticsDate == "month"'>
                AND YEAR(REPORT_DATE) = YEAR(CURDATE())
                AND MONTH(REPORT_DATE) = MONTH(CURDATE())
            </when>
            <when test='statisticsDate == "lastMonth"'>
                AND REPORT_DATE BETWEEN
                DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01 00:00:00')
                AND DATE_FORMAT(LAST_DAY(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)), '%Y-%m-%d 23:59:59')
            </when>
        </choose>
        <if test="submitTpa != null and submitTpa !=''">
            AND submit_tpa = #{submitTpa}
        </if>
        <if test="submitter != null and submitter !=''">
            AND submitter = #{submitter}
        </if>
        ),
        case_stats AS (
        SELECT
        COUNT(*) as case_number
        FROM common_data
        WHERE task_bpm_key IN ('02','04','07','09')
        AND END_CASE_DATE IS NOT NULL
        <choose>
            <when test='statisticsDate == "day"'>
                AND DATE(END_CASE_DATE) = CURDATE()
            </when>
            <when test='statisticsDate == "week"'>
                AND YEAR(END_CASE_DATE) = YEAR(CURDATE())
                AND WEEK(END_CASE_DATE, 1) = WEEK(CURDATE(), 1)
            </when>
            <when test='statisticsDate == "month"'>
                AND YEAR(END_CASE_DATE) = YEAR(CURDATE())
                AND MONTH(END_CASE_DATE) = MONTH(CURDATE())
            </when>
            <when test='statisticsDate == "lastMonth"'>
                AND END_CASE_DATE BETWEEN
                DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01 00:00:00')
                AND DATE_FORMAT(LAST_DAY(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)), '%Y-%m-%d 23:59:59')
            </when>
        </choose>
        <if test="submitTpa != null and submitTpa !=''">
            AND submit_tpa = #{submitTpa}
        </if>
        <if test="submitter != null and submitter !=''">
            AND submitter = #{submitter}
        </if>
        ),
        reopen_stats AS (
        SELECT
        COUNT(*) as reopen_number
        FROM common_data
        WHERE approval_reopen_date IS NOT NULL
        <choose>
            <when test='statisticsDate == "day"'>
                AND DATE(approval_reopen_date) = CURDATE()
            </when>
            <when test='statisticsDate == "week"'>
                AND YEAR(approval_reopen_date) = YEAR(CURDATE())
                AND WEEK(approval_reopen_date, 1) = WEEK(CURDATE(), 1)
            </when>
            <when test='statisticsDate == "month"'>
                AND YEAR(approval_reopen_date) = YEAR(CURDATE())
                AND MONTH(approval_reopen_date) = MONTH(CURDATE())
            </when>
            <when test='statisticsDate == "lastMonth"'>
                AND approval_reopen_date BETWEEN
                DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01 00:00:00')
                AND DATE_FORMAT(LAST_DAY(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)), '%Y-%m-%d 23:59:59')
            </when>
        </choose>
        <if test="submitTpa != null and submitTpa !=''">
            AND submit_tpa = #{submitTpa}
        </if>
        <if test="submitter != null and submitter !=''">
            AND submitter = #{submitter}
        </if>
        ),
        average_pay_stats AS (
        SELECT
        ROUND(SUM(POLICY_PAY)/COUNT(*), 2) as average_pay
        FROM common_data
        WHERE task_bpm_key IN ('02','04','07','09')
        AND END_CASE_DATE IS NOT NULL
        <choose>
            <when test='statisticsDate == "day"'>
                AND DATE(END_CASE_DATE) = CURDATE()
            </when>
            <when test='statisticsDate == "week"'>
                AND YEAR(END_CASE_DATE) = YEAR(CURDATE())
                AND WEEK(END_CASE_DATE, 1) = WEEK(CURDATE(), 1)
            </when>
            <when test='statisticsDate == "month"'>
                AND YEAR(END_CASE_DATE) = YEAR(CURDATE())
                AND MONTH(END_CASE_DATE) = MONTH(CURDATE())
            </when>
            <when test='statisticsDate == "lastMonth"'>
                AND END_CASE_DATE BETWEEN
                DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01 00:00:00')
                AND DATE_FORMAT(LAST_DAY(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)), '%Y-%m-%d 23:59:59')
            </when>
        </choose>
        <if test="submitTpa != null and submitTpa !=''">
            AND submit_tpa = '' AND task_bpm_key IN ('07')
        </if>
        <if test="submitter != null and submitter !=''">
            AND submitter = '' AND task_bpm_key IN ('07')
        </if>
        ),
        filing_cycle_stats AS (
        SELECT
        FLOOR(SUM(DATEDIFF(AUDIT_DATE, report_time))/COUNT(*)) as filing_cycle
        FROM common_data
        WHERE task_bpm_key='01'
        AND AUDIT_DATE IS NOT NULL
        AND report_time IS NOT NULL
        <choose>
            <when test='statisticsDate == "day"'>
                AND DATE(REPORT_DATE) = CURDATE()
            </when>
            <when test='statisticsDate == "week"'>
                AND YEAR(REPORT_DATE) = YEAR(CURDATE())
                AND WEEK(REPORT_DATE, 1) = WEEK(CURDATE(), 1)
            </when>
            <when test='statisticsDate == "month"'>
                AND YEAR(REPORT_DATE) = YEAR(CURDATE())
                AND MONTH(REPORT_DATE) = MONTH(CURDATE())
            </when>
            <when test='statisticsDate == "lastMonth"'>
                AND REPORT_DATE BETWEEN
                DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01 00:00:00')
                AND DATE_FORMAT(LAST_DAY(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)), '%Y-%m-%d 23:59:59')
            </when>
        </choose>
        <if test="submitTpa != null and submitTpa !=''">
            AND submit_tpa = #{submitTpa}
        </if>
        <if test="submitter != null and submitter !=''">
            AND submitter = #{submitter}
        </if>
        ),
        case_cycle_stats AS (
        SELECT
        FLOOR(SUM(DATEDIFF(end_case_date, report_time))/COUNT(*)) as case_cycle
        FROM common_data
        WHERE task_bpm_key IN ('02','04','07','09')
        AND END_CASE_DATE IS NOT NULL
        <choose>
            <when test='statisticsDate == "day"'>
                AND DATE(END_CASE_DATE) = CURDATE()
            </when>
            <when test='statisticsDate == "week"'>
                AND YEAR(END_CASE_DATE) = YEAR(CURDATE())
                AND WEEK(END_CASE_DATE, 1) = WEEK(CURDATE(), 1)
            </when>
            <when test='statisticsDate == "month"'>
                AND YEAR(END_CASE_DATE) = YEAR(CURDATE())
                AND MONTH(END_CASE_DATE) = MONTH(CURDATE())
            </when>
            <when test='statisticsDate == "lastMonth"'>
                AND END_CASE_DATE BETWEEN
                DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01 00:00:00')
                AND DATE_FORMAT(LAST_DAY(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)), '%Y-%m-%d 23:59:59')
            </when>
        </choose>
        <if test="submitTpa != null and submitTpa !=''">
            AND submit_tpa = #{submitTpa}
        </if>
        <if test="submitter != null and submitter !=''">
            AND submitter = #{submitter}
        </if>
        ),
        tpa_pay_stats AS (
        SELECT
        ROUND(SUM(amount), 2) as tpa_pay
        FROM common_data
        <where>
            <choose>
                <when test='statisticsDate == "day"'>
                    AND DATE(END_CASE_DATE) = CURDATE()
                </when>
                <when test='statisticsDate == "week"'>
                    AND YEAR(END_CASE_DATE) = YEAR(CURDATE())
                    AND WEEK(END_CASE_DATE, 1) = WEEK(CURDATE(), 1)
                </when>
                <when test='statisticsDate == "month"'>
                    AND YEAR(END_CASE_DATE) = YEAR(CURDATE())
                    AND MONTH(END_CASE_DATE) = MONTH(CURDATE())
                </when>
                <when test='statisticsDate == "lastMonth"'>
                    AND END_CASE_DATE BETWEEN
                    DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01 00:00:00')
                    AND DATE_FORMAT(LAST_DAY(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)), '%Y-%m-%d 23:59:59')
                </when>
            </choose>
            <if test="submitTpa != null and submitTpa !=''">
                AND submit_tpa = ''
            </if>
            <if test="submitter != null and submitter !=''">
                AND submitter = ''
            </if>
        </where>
        ),
        errors_stats AS (
        SELECT
        COUNT(*) as errors_number
        FROM clms_tpa_settle_info
        WHERE (task_bpm_key IN ('02','04','07','09')
        OR (task_bpm_key IN ('03','05','10','11') AND opt_type='1'))
        AND is_valid ='0'
        <choose>
            <when test='statisticsDate == "day"'>
                AND DATE(sys_utime) = CURDATE()
            </when>
            <when test='statisticsDate == "week"'>
                AND YEAR(sys_utime) = YEAR(CURDATE())
                AND WEEK(sys_utime, 1) = WEEK(CURDATE(), 1)
            </when>
            <when test='statisticsDate == "month"'>
                AND YEAR(sys_utime) = YEAR(CURDATE())
                AND MONTH(sys_utime) = MONTH(CURDATE())
            </when>
            <when test='statisticsDate == "lastMonth"'>
                AND sys_utime BETWEEN
                DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01 00:00:00')
                AND DATE_FORMAT(LAST_DAY(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)), '%Y-%m-%d 23:59:59')
            </when>
        </choose>
        <if test="submitTpa != null and submitTpa !=''">
            AND submit_tpa = #{submitTpa}
        </if>
        <if test="submitter != null and submitter !=''">
            AND submitter = #{submitter}
        </if>
        )
        SELECT
        rcs.report_case_number,
        cs.case_number,
        rs.reopen_number,
        aps.average_pay,
        fcs.filing_cycle,
        ccs.case_cycle,
        tps.tpa_pay,
        es.errors_number,
        ROUND((cs.case_number / NULLIF(rcs.report_case_number, 0)), 2) * 100 AS case_rate
        FROM
        report_case_stats rcs,
        case_stats cs,
        reopen_stats rs,
        average_pay_stats aps,
        filing_cycle_stats fcs,
        case_cycle_stats ccs,
        tpa_pay_stats tps,
        errors_stats es
    </select>

    <select id="getTpaPendingInfo" parameterType="com.paic.ncbs.claim.model.vo.tpa.TpaHomeworkStatusRequestVo"
            resultMap="PendingInfoResultMap">
        WITH main_handler AS (
        SELECT
        cts.report_no,
        cts.case_times,
        CASE
        WHEN est.latest_estimator IS NOT NULL THEN est.latest_estimator
        WHEN rec.latest_receiver IS NOT NULL THEN rec.latest_receiver
        ELSE reg.tracking_handler
        END AS main_processor,
        CASE
        WHEN est.est_tpa IS NOT NULL THEN est.est_tpa
        WHEN rec.rec_tpa IS NOT NULL THEN rec.rec_tpa
        ELSE reg.reg_tpa
        END AS main_processor_tpa
        FROM clms_tpa_settle_info cts
        LEFT JOIN (
        SELECT
        tsi.report_no,
        tsi.case_times,
        tsi.submitter AS latest_estimator,
        tsi.submit_tpa AS est_tpa
        FROM (
        SELECT
        report_no,
        case_times,
        submitter,
        submit_tpa,
        ROW_NUMBER() OVER (PARTITION BY report_no, case_times ORDER BY sys_ctime DESC) as rn
        FROM clms_tpa_settle_info
        WHERE task_bpm_key = '07' AND is_valid = '1'
        ) tsi
        WHERE tsi.rn = 1
        ) est ON cts.report_no = est.report_no AND cts.case_times = est.case_times
        LEFT JOIN (
        SELECT
        tsi2.report_no,
        tsi2.case_times,
        tsi2.submitter AS latest_receiver,
        tsi2.submit_tpa AS rec_tpa
        FROM (
        SELECT
        report_no,
        case_times,
        submitter,
        submit_tpa,
        ROW_NUMBER() OVER (PARTITION BY report_no, case_times ORDER BY sys_ctime DESC) as rn
        FROM clms_tpa_settle_info
        WHERE task_bpm_key = '06' AND is_valid = '1'
        ) tsi2
        WHERE tsi2.rn = 1
        ) rec ON cts.report_no = rec.report_no AND cts.case_times = rec.case_times
        LEFT JOIN (
        SELECT
        tsi3.report_no,
        tsi3.case_times,
        tsi3.submitter AS tracking_handler,
        tsi3.submit_tpa AS reg_tpa
        FROM clms_tpa_settle_info tsi3
        WHERE tsi3.task_bpm_key = '01' AND tsi3.is_valid = '1'
        ) reg ON cts.report_no = reg.report_no AND cts.case_times = reg.case_times
        WHERE cts.is_valid = '1'
        GROUP BY cts.report_no, cts.case_times
        ),
        base_data AS (
        SELECT DISTINCT
        SUBSTRING_INDEX(cts.report_no, '-', 1) AS main_report_no,
        cts.report_no AS cts_report_no,
        cts.case_times AS cts_case_times,
        cer.ESTIMATE_AMOUNT AS undetermined_amount,
        cri.report_date,
        pi.product_code,
        mh.main_processor,
        mh.main_processor_tpa,
        CASE WHEN cpd_disability.cnt > 0 THEN 1 ELSE 0 END AS has_disability,
        CASE WHEN cpd_death.cnt > 0 THEN 1 ELSE 0 END AS has_death
        FROM clms_tpa_settle_info cts
        INNER JOIN main_handler mh
        ON cts.report_no = mh.report_no
        AND cts.case_times = mh.case_times
        LEFT JOIN clm_case_base cb
        ON cts.report_no = cb.report_no
        AND cts.case_times = cb.case_times
        LEFT JOIN CLM_REPORT_INFO cri
        ON cts.report_no = cri.report_no
        LEFT JOIN clms_estimate_record cer
        ON cts.report_no = cer.report_no
        AND cts.case_times = cer.case_times
        LEFT JOIN clms_policy_info pi
        ON cts.report_no = pi.report_no
        LEFT JOIN (
        SELECT
        cpd.report_no,
        cpd.case_times,
        COUNT(*) AS cnt
        FROM clms_person_disability cpd
        WHERE cpd.IS_EFFECTIVE = 'Y' AND STATUS ='1'
        GROUP BY cpd.report_no, cpd.case_times
        ) cpd_disability
        ON cts.report_no = cpd_disability.report_no
        AND cts.case_times = cpd_disability.case_times
        LEFT JOIN (
        SELECT
        cpd2.report_no,
        cpd2.case_times,
        COUNT(*) AS cnt
        FROM clms_person_death cpd2
        WHERE cpd2.IS_EFFECTIVE = 'Y' AND STATUS ='1'
        GROUP BY cpd2.report_no, cpd2.case_times
        ) cpd_death
        ON cts.report_no = cpd_death.report_no
        AND cts.case_times = cpd_death.case_times
        WHERE cts.is_valid = '1'
            AND EXISTS (
            SELECT 1
            FROM clm_whole_case_base wcb
            WHERE wcb.report_no = cts.report_no
            AND wcb.case_times = cts.case_times
            AND wcb.WHOLE_CASE_STATUS != '0'
            )
        <if test="departmentCodes != null and departmentCodes != ''">
            AND cb.DEPARTMENT_CODE IN
            <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="submitTpa != null and submitTpa != ''">
            AND mh.main_processor_tpa = #{submitTpa}
        </if>
        <if test="submitter != null and submitter != ''">
            AND mh.main_processor = #{submitter}
        </if>
        <if test="productCode != null and productCode != ''">
            AND pi.product_code = #{productCode}
        </if>
        ),
        pending_stats AS (
        SELECT
        COUNT(DISTINCT main_report_no) AS total_pending_count,
        SUM(has_disability) AS disability_pending_count,
        SUM(has_death) AS death_pending_count,
        SUM(undetermined_amount) AS total_undetermined_amount,
        SUM(DATEDIFF(NOW(), report_date)) AS total_days_diff
        FROM base_data
        )
        SELECT
        ps.total_pending_count AS pendingCaseCount,
        ps.disability_pending_count AS disabilityPendingCount,
        ps.death_pending_count AS deathPendingCount,
        CASE
        WHEN ps.total_pending_count > 0
        THEN ROUND(COALESCE(ps.total_undetermined_amount, 0) / ps.total_pending_count, 2)
        ELSE 0
        END AS avgUndeterminedAmount,
        CASE
        WHEN ps.total_pending_count > 0
        THEN ROUND(ps.total_days_diff / ps.total_pending_count, 2)
        ELSE 0
        END AS avgPendingDays
        FROM pending_stats ps
    </select>

    <select id="getInvestigateStats" parameterType="com.paic.ncbs.claim.model.vo.tpa.TpaHomeworkStatusRequestVo"
            resultType="com.paic.ncbs.claim.model.vo.tpa.InvestigateStatsVo">
        WITH common_data AS (
        SELECT
        i.report_no,
        i.case_times,
        i.INVESTIGATE_STATUS,
        i.INITIATE_DEPARTMENT,
        i.UPDATED_DATE AS approve_date,
        cb.DEPARTMENT_CODE AS case_department_code,
        pi.product_code,
        lr.REDUCE_AMOUNT
        FROM clms_investigate i
        LEFT JOIN clm_case_base cb ON i.report_no = cb.report_no AND i.case_times = cb.case_times
        LEFT JOIN clms_policy_info pi ON i.report_no = pi.report_no
        LEFT JOIN clms_loss_reduce lr ON i.report_no = lr.report_no AND i.case_times = lr.case_times AND lr.STATUS = '1' and lr.IS_EFFECTIVE = 'Y'
        WHERE i.INVESTIGATE_STATUS IN ('3', '4')
        AND i.UPDATED_DATE IS NOT NULL
        AND i.report_no IS NOT NULL
        AND i.case_times IS NOT NULL
        ),
        filtered_data AS (
        SELECT *
        FROM common_data
        WHERE 1=1
        <if test="departmentCodes != null and departmentCodes != ''">
            AND case_department_code IN
            <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="productCode != null and productCode != ''">
            AND product_code = #{productCode}
        </if>
        <if test="initiateDepartment != null and initiateDepartment != ''">
            AND INITIATE_DEPARTMENT = #{initiateDepartment}
        </if>
        <choose>
            <when test='statisticsDate == "day"'>
                AND DATE(approve_time) = CURDATE()
            </when>
            <when test='statisticsDate == "week"'>
                AND YEAR(approve_time) = YEAR(CURDATE())
                AND WEEK(approve_time, 1) = WEEK(CURDATE(), 1)
            </when>
            <when test='statisticsDate == "month"'>
                AND YEAR(approve_time) = YEAR(CURDATE())
                AND MONTH(approve_time) = MONTH(CURDATE())
            </when>
            <when test='statisticsDate == "lastMonth"'>
                AND approve_time BETWEEN
                DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01 00:00:00')
                AND DATE_FORMAT(LAST_DAY(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)), '%Y-%m-%d 23:59:59')
            </when>
        </choose>
        )
        SELECT
        COUNT(CASE WHEN INVESTIGATE_STATUS = '3' THEN 1 END) AS dispatchCount,
        COUNT(CASE WHEN INVESTIGATE_STATUS = '4' THEN 1 END) AS investigationCount,
        COALESCE(SUM(REDUCE_AMOUNT), 0) AS totalReduceAmount,
        ROUND(COALESCE(SUM(REDUCE_AMOUNT), 0) / NULLIF(COUNT(CASE WHEN INVESTIGATE_STATUS = '4' THEN 1 END), 0), 2) AS avgReduceAmount
        FROM filtered_data;
    </select>

    <select id="getPendingInvestigateStats" parameterType="com.paic.ncbs.claim.model.vo.tpa.TpaHomeworkStatusRequestVo"
            resultType="com.paic.ncbs.claim.model.vo.tpa.PendingInvestigateStatsVo">
        WITH common_data AS (
        SELECT
        i.report_no,
        i.case_times,
        i.INITIATE_DEPARTMENT,
        i.UPDATED_DATE AS approve_date,
        i.INVESTIGATE_STATUS,
        cb.DEPARTMENT_CODE AS case_department_code,
        cri.REPORT_DATE
        FROM clms_investigate i
        LEFT JOIN clm_case_base cb ON i.report_no = cb.report_no AND i.case_times = cb.case_times
        LEFT JOIN CLM_REPORT_INFO cri ON i.report_no = cri.report_no
        WHERE i.INVESTIGATE_STATUS = '3'
        AND i.report_no IS NOT NULL
        AND i.case_times IS NOT NULL
        ),
        disability_data AS (
        SELECT DISTINCT
        cpd.report_no,
        cpd.case_times
        FROM clms_person_disability cpd
        WHERE cpd.IS_EFFECTIVE = 'Y'
        AND cpd.STATUS = '1'
        ),
        death_data AS (
        SELECT DISTINCT
        cpd.report_no,
        cpd.case_times
        FROM clms_person_death cpd
        WHERE cpd.IS_EFFECTIVE = 'Y'
        AND cpd.STATUS = '1'
        ),
        filtered_data AS (
        SELECT
        cd.*,
        dd.report_no AS disability_report_no,
        dt.report_no AS death_report_no,
        DATEDIFF(NOW(), cd.approve_date) AS days_since_approval,
        DATEDIFF(NOW(), cd.REPORT_DATE) AS days_since_report
        FROM common_data cd
        LEFT JOIN disability_data dd
        ON cd.report_no = dd.report_no
        AND cd.case_times = dd.case_times
        LEFT JOIN death_data dt
        ON cd.report_no = dt.report_no
        AND cd.case_times = dt.case_times
        )
        SELECT
        COUNT(*) AS pendingCount,
        COUNT(CASE WHEN days_since_approval > 60 THEN 1 END) AS overdue60DaysCount,
        COUNT(CASE WHEN disability_report_no IS NOT NULL THEN 1 END) AS disabilityCount,
        COUNT(CASE WHEN death_report_no IS NOT NULL THEN 1 END) AS deathCount,
        COUNT(CASE WHEN days_since_report > 90 THEN 1 END) AS caseOver90DaysCount,
        COUNT(CASE WHEN days_since_report > 180 THEN 1 END) AS caseOver180DaysCount,
        COUNT(CASE WHEN days_since_report > 360 THEN 1 END) AS caseOver360DaysCount
        FROM filtered_data
        WHERE 1=1
        <if test="departmentCodes != null and departmentCodes != ''">
            AND case_department_code IN
            <foreach collection="departmentCodes" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="initiateDepartment != null and initiateDepartment != ''">
            AND INITIATE_DEPARTMENT = #{initiateDepartment}
        </if>
    </select>

</mapper>
