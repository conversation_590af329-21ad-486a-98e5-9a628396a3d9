package com.paic.ncbs.claim.dao.mapper.report;

import com.paic.ncbs.claim.model.dto.ocas.OcasInsuredDTO;
import com.paic.ncbs.claim.model.dto.ocas.OcasPolicyDTO;
import com.paic.ncbs.claim.model.dto.report.PolicyRiskSubPropDTO;
import com.paic.ncbs.claim.model.vo.report.PolicyQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface PolicyMapper {

    List<OcasPolicyDTO> getPolicyList(PolicyQueryVO queryVO);

    List<OcasInsuredDTO> getPolicyInsuredList(PolicyQueryVO queryVO);

    List<PolicyRiskSubPropDTO> getPolicyRiskSubPropList(PolicyQueryVO queryVO);

    Date querySurrender(@Param("policyNo") String policyNo);

}
