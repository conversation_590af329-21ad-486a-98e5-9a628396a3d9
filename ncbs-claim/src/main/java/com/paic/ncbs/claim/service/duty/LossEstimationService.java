package com.paic.ncbs.claim.service.duty;

import com.paic.ncbs.claim.model.dto.duty.LossEstimationDTO;
import com.paic.ncbs.claim.model.vo.duty.AccidentLossVo;
import com.paic.ncbs.claim.model.vo.duty.LossEstimationVO;

import java.util.List;
import java.util.Map;

public interface LossEstimationService {

    /**
     * 获取估损列表
     * @param reportNo
     * @param caseTimes
     * @param taskId
     * @return
     */
    Map<String,List<LossEstimationVO>> getLossEstimationVOs(String reportNo, int caseTimes, String taskId);
    /**
     * 获取最新估损列表
     * @param reportNo
     * @param caseTimes
     * @return
     */
    Map<String, List<LossEstimationVO>> getLastLossEstimationVOs(String reportNo, int caseTimes);
    /**
     * 删除估损信息
     * @param reportNo 报案号
     * @param caseTimes 报案次数
     * @param taskId 节点id
     */
    void removeLossEstimation(String reportNo, int caseTimes, String taskId);
    /**
     * 保存估损信息
     *
     * @param reportNo          报案号
     * @param caseTimes         报案次数
     * @param taskId            节点id
     * @param lossClass
     * @param lossEstimationVos 估损信息
     */
    void saveLossEstimationList(String reportNo, int caseTimes, String taskId, String lossClass, Map<String, List<LossEstimationVO>> lossEstimationVos);
    /**
     * 更新估损信息的taskId
     *
     * @param
     */
    void updateLossEstimationTaskId(String reportNo, int caseTimes, String taskId,String idFlagHistoryChange);

    AccidentLossVo getAccisentLossVO(String reportNo, int caseTimes, String taskId);
    /**
     * 获取最新一条估损信息
     *
     * @param reportNo  报案号
     * @param caseTimes 报案次数
     * @return
     */
    List<LossEstimationDTO> getLastLossEstimationVOList(String reportNo, int caseTimes);
    /**
     * 保存估损信息
     * @param lossEstimationDTO
     */
    void saveLossEstimation(LossEstimationDTO lossEstimationDTO);
}
