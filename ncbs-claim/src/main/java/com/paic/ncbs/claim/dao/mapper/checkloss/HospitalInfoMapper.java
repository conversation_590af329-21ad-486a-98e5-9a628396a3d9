package com.paic.ncbs.claim.dao.mapper.checkloss;

import com.paic.ncbs.claim.model.dto.duty.PersonHospitalDTO;
import com.paic.ncbs.claim.model.dto.endcase.CaseClassDTO;
import com.paic.ncbs.claim.model.vo.checkloss.HospitalInfoVO;
import com.paic.ncbs.claim.model.vo.duty.PersonHospitalVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

@MapperScan
public interface HospitalInfoMapper {

    public List<HospitalInfoVO> getHospitalList(@Param("hospitalName") String hospitalName, @Param("orgType") String orgType);

    public HospitalInfoVO getHospitalByCode(@Param("hospitalCode") String hospitalCode);

    public HospitalInfoVO getHospitalCodeByName(@Param("hospitalName") String hospitalName,@Param("orgType")String orgType);

    public List<PersonHospitalDTO> getHostpitalvo(String idAhcsChannelProcess, String taskId, String status);

    void saveHospitalInfo(HospitalInfoVO hospitalInfoVO);

    void updateHospitalInfo(HospitalInfoVO hospitalInfoVO);

    public HospitalInfoVO getHospitalByKey(@Param("idHospitalInfo") String idHospitalInfo);

    public List<HospitalInfoVO> getHospitalInfoList(HospitalInfoVO hospitalInfoVO);

    public Integer getHospitalInfoCount(HospitalInfoVO hospitalInfoVO);
}
