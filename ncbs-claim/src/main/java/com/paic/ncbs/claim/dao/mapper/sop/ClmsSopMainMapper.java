package com.paic.ncbs.claim.dao.mapper.sop;

import com.paic.ncbs.claim.dao.entity.sop.ClmsSopMain;
import com.paic.ncbs.claim.model.vo.sop.SopHistoryMainVO;
import com.paic.ncbs.claim.model.vo.sop.SopMainVO;
import com.paic.ncbs.claim.model.vo.sop.SopQueryVO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;

/**
 * SOP信息表Mapper接口
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@MapperScan
public interface ClmsSopMainMapper {

    /**
     * 查询SOP列表
     *
     * @param queryVO 查询条件
     * @return SOP列表
     */
    List<SopMainVO> selectSopList(@Param("queryVO") SopQueryVO queryVO);

    /**
     * 根据ID查询SOP
     *
     * @param idSopMain SOP主键
     * @return SOP信息
     */
    ClmsSopMain selectByPrimaryKey(@Param("idSopMain") String idSopMain);

    /**
     * 插入SOP
     *
     * @param record SOP信息
     * @return 影响行数
     */
    int insert(ClmsSopMain record);

    /**
     * 选择性插入SOP
     *
     * @param record SOP信息
     * @return 影响行数
     */
    int insertSelective(ClmsSopMain record);

    /**
     * 选择性更新SOP
     *
     * @param record SOP信息
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(ClmsSopMain record);

    /**
     * 更新SOP
     *
     * @param record SOP信息
     * @return 影响行数
     */
    int updateByPrimaryKey(ClmsSopMain record);

    /**
     * 根据SOP名称查询有效的SOP数量
     *
     * @param sopName SOP名称
     * @param excludeId 排除的ID
     * @return 数量
     */
    int countValidSopByName(@Param("sopName") String sopName, @Param("excludeId") String excludeId);

    /**
     * 根据SOP名称查询所有版本
     *
     * @param sopName SOP名称
     * @return SOP版本列表
     */
    List<ClmsSopMain> selectVersionsBySopName(@Param("sopName") String sopName);

    /**
     * 查询匹配的SOP列表
     *
     * @param productCode 产品代码
     * @param groupCode 方案代码
     * @param planCode 险种代码
     * @param taskBpmKey 环节代码
     * @return 匹配的SOP列表
     */
    List<ClmsSopMain> selectMatchingSops(@Param("productCode") String productCode,
                                         @Param("groupCode") String groupCode,
                                         @Param("planCode") String planCode,
                                         @Param("taskBpmKey") String taskBpmKey);

    /**
     * 批量更新SOP状态为无效
     *
     * @param sopName SOP名称
     * @param excludeId 排除的ID
     * @param updatedBy 修改人
     * @return 影响行数
     */
    int batchUpdateStatusToInvalid(@Param("sopName") String sopName,
                                   @Param("excludeId") String excludeId,
                                   @Param("updatedBy") String updatedBy);

    /**
     * 根据ID查询SOP详情
     *
     * @param idSopMain SOP主键
     * @return SOP详情
     */
    com.paic.ncbs.claim.model.vo.sop.SopMainVO selectSopDetailById(@Param("idSopMain") String idSopMain);

    /**
     * 根据ID删除SOP
     *
     * @param idSopMain SOP主键
     * @return 影响行数
     */
    int deleteByPrimaryKey(@Param("idSopMain") String idSopMain);

    /**
     * 查询SOP版本列表
     *
     * @param batchNo 批次
     * @return 版本列表
     */
    List<SopHistoryMainVO> selectListByBatchNo(@Param("batchNo") String batchNo);

    /**
     * 查询匹配的SOP规则
     *
     * @param productCode 产品代码
     * @param groupCode 方案代码
     * @param planCode 险种代码
     * @param taskBpmKey 环节代码
     * @return 匹配的SOP规则
     */
    List<com.paic.ncbs.claim.model.vo.sop.SopMainVO> selectMatchingSopRules(@Param("productCode") String productCode,
                                                                             @Param("groupCode") String groupCode,
                                                                             @Param("planCode") String planCode,
                                                                             @Param("taskBpmKey") String taskBpmKey);

    /**
     * 查询最大版本号
     *
     * @param sopName SOP名称
     * @return 最大版本号
     */
    String selectMaxVersionBySopName(@Param("sopName") String sopName);

    /**
     * 根据SOP名称查询数量
     *
     * @param sopName SOP名称
     * @param excludeId 排除的ID
     * @return 数量
     */
    int countBySopName(@Param("sopName") String sopName, @Param("excludeId") String excludeId);

    /**
     * 查询有效的SOP列表
     *
     * @return 有效的SOP列表
     */
    List<com.paic.ncbs.claim.model.vo.sop.SopMainVO> selectValidSopList();

    /**
     * 根据SOP名称查询SOP
     *
     * @param sopName SOP名称
     * @return SOP信息
     */
    ClmsSopMain selectBySopName(@Param("sopName") String sopName);

    /**
     * 根据批次号查询最新一条数据
     *
     * @param batchNo 批次号
     * @return SOP信息
     */
    ClmsSopMain selectLatestByBatchNo(@Param("batchNo") String batchNo);

    /**
     * 根据批次号更新失效日期
     *
     * @param batchNo 批次号
     * @param excludeId 排除的ID
     * @param updatedBy 修改人
     * @param invalidDate 失效日期
     * @return 影响行数
     */
    int updateInvalidDateByBatchNo(@Param("batchNo") String batchNo,
                                   @Param("excludeId") String excludeId,
                                   @Param("updatedBy") String updatedBy,
                                   @Param("invalidDate") java.time.LocalDateTime invalidDate);

    /**
     * 查询险种信息列表
     *
     * @return 险种信息列表（PLAN_CODE, PLAN_CHINESE_NAME）
     */
    List<Object> selectPlanInfoList();

    /**
     * 根据案件信息匹配SOP规则（在SQL中直接查询案件信息）
     *
     * @param reportNo 报案号
     * @param caseTimes 案件次数
     *
     * @return 匹配的SOP规则列表
     */
    List<com.paic.ncbs.claim.model.vo.sop.SopMainVO> selectMatchingSopRulesByCase(@Param("reportNo") String reportNo,
                                                                                   @Param("caseTimes") Integer caseTimes,
                                                                                   @Param("taskBpmKey") String taskBpmKey);

    /**
     * 查询产品与方案的关联关系
     *
     * @return 产品方案关联关系列表（Map结构包含productCode和groupCode）
     */
    List<java.util.Map<String, Object>> selectProductGroupAssociations();

}
