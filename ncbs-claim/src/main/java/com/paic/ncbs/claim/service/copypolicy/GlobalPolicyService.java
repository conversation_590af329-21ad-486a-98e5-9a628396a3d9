package com.paic.ncbs.claim.service.copypolicy;

import com.alibaba.fastjson.JSONObject;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.model.dto.ocas.OcasInsuredDTO;
import com.paic.ncbs.claim.model.dto.ocas.OcasPolicyDTO;
import com.paic.ncbs.claim.model.dto.ocas.OcasPolicyPlanDutyDTO;
import com.paic.ncbs.claim.model.dto.report.CopyPolicyQueryVO;
import com.paic.ncbs.claim.model.vo.report.PolicyQueryVO;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


@Component
public interface GlobalPolicyService {

    List<OcasPolicyDTO> getpolicyList(PolicyQueryVO queryVO);

    List<OcasInsuredDTO> getpolicyInsuredList(PolicyQueryVO queryVO);

    String getPolicyDetail(CopyPolicyQueryVO vo);

    List<OcasPolicyPlanDutyDTO> getPolicyDutyDetail(CopyPolicyQueryVO vo);

    ResponseResult<Object> returnReportToGlobal(String reportNo);

    ResponseResult<Object> returnRegisterToGlobal(String reportNo);

    ResponseResult<JSONObject> returnEstimateLoss(String reportNo,String taskType,Integer caseTimes);

    void sendReturnEstimateLoss(String reportNo, String taskType, Integer caseTimes);

    void sendReturnSettleToGlobal(String reportNo, Integer caseTimes,String closeType);

    ResponseResult<Object> returnSettleToGlobal(String reportNo, Integer caseTimes,String closeType);

    void sendReturnRegisterAndEstimateLoss(String reportNo,Integer caseTimes);

    boolean checkGlobalPolicyNo(String policyNo);

    ResponseResult<JSONObject> returnRestartGlobal(String reportNo,Integer caseTimes,String policyNo);

    void sendRestartToGlobal(String reportNo, Integer caseTimes);

    void sendReturnCloseToGlobal(String reportNo, Integer caseTimes,String closeType);

    Map<String, BigDecimal> getHisPayAmount(CopyPolicyQueryVO copyPolicyQueryVO);
}
