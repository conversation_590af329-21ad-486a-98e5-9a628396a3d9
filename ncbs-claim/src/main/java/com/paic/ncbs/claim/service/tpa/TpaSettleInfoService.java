package com.paic.ncbs.claim.service.tpa;

import com.baomidou.mybatisplus.extension.service.IService;
import com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaSettleInfo;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.investigate.TpaSupplierInfoListVO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * TPA结算信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
public interface TpaSettleInfoService extends IService<ClmsTpaSettleInfo> {
    void saveTpaSettleInfo(ClmsTpaSettleInfo clmsTpaSettleInfo,String wholeCaseStatus);

    void validateRuleAndSaveInfo(ClmsTpaSettleInfo clmsTpaSettleInfo);

    void delSettleInfo(ClmsTpaSettleInfo clmsTpaSettleInfo);
}
