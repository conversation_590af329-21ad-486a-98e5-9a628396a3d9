package com.paic.ncbs.claim.dao.mapper.tpa;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaSettleInfo;
import com.paic.ncbs.claim.model.vo.tpa.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * TPA结算信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
public interface ClmsTpaSettleInfoMapper extends BaseMapper<ClmsTpaSettleInfo> {
   void delSettleInfo (ClmsTpaSettleInfo clmsTpaSettleInfo);

   void updateSettleInfo (ClmsTpaSettleInfo clmsTpaSettleInfo);

   void updateSettleStatus (ClmsTpaSettleInfo clmsTpaSettleInfo);

   List<ClmsTpaSettleInfo> selectTpaSettleInfo(ClmsTpaSettleInfo clmsTpaSettleInfo);

   void saveSettleInfo(@Param("settleInfoList") List<ClmsTpaSettleInfo> settleInfoList);

   TpaHomeworkStatusVo getTpaHomeworkStatus(TpaHomeworkStatusRequestVo queryVO);

   TpaPendingInfoVo getTpaPendingInfo(TpaHomeworkStatusRequestVo queryVO);

   InvestigateStatsVo getInvestigateStats(TpaHomeworkStatusRequestVo queryVO);

   PendingInvestigateStatsVo getPendingInvestigateStats(TpaHomeworkStatusRequestVo queryVO);
}
