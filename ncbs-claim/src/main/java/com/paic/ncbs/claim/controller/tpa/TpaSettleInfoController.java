package com.paic.ncbs.claim.controller.tpa;

import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.controller.BaseController;
import com.paic.ncbs.claim.dao.mapper.tpa.ClmsTpaSettleInfoMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyFormDTO;
import com.paic.ncbs.claim.model.dto.user.DepartmentDTO;
import com.paic.ncbs.claim.model.vo.tpa.TpaHomeworkStatusRequestVo;
import com.paic.ncbs.claim.model.vo.tpa.TpaHomeworkStatusVo;
import com.paic.ncbs.claim.service.other.impl.TaskListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "TPA管理")
@RestController
@RequestMapping("tpa/settleInfo")
@Slf4j
public class TpaSettleInfoController  {
    @Autowired
    private ClmsTpaSettleInfoMapper clmsTpaSettleInfoMapper;
    @Autowired
    private TaskListService taskListService;
    /**
     * TPA看板-作业情况
     */
    @ApiOperation("查看TPA看板-作业情况")
    @PostMapping(value = "/getTpaHomeworkStatus", produces = {"application/json"})
    public ResponseResult<Object> getTpaHomeworkStatus(@RequestBody TpaHomeworkStatusRequestVo queryVO) {
        try {
            List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(queryVO.getDepartmentCode());
            queryVO.setDepartmentCodes(departmentCodes);
            return ResponseResult.success(clmsTpaSettleInfoMapper.getTpaHomeworkStatus(queryVO));
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
    }
    /**
     * TPA看板-未决情况
     */
    @ApiOperation("查看TPA看板-未决情况")
    @PostMapping(value = "/getTpaPendingInfo", produces = {"application/json"})
    public ResponseResult<Object> getTpaPendingInfo(@RequestBody TpaHomeworkStatusRequestVo queryVO) {
        try {
            List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(queryVO.getDepartmentCode());
            queryVO.setDepartmentCodes(departmentCodes);
            return ResponseResult.success(clmsTpaSettleInfoMapper.getTpaPendingInfo(queryVO));
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
    }
    /**
     *  调查看板-作业情况
     */
    @ApiOperation("调查看板-作业情况")
    @PostMapping(value = "/getInvestigateStats", produces = {"application/json"})

    public ResponseResult<Object> getInvestigateStats(@RequestBody TpaHomeworkStatusRequestVo queryVO) {
        try {
            List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(queryVO.getDepartmentCode());
            queryVO.setDepartmentCodes(departmentCodes);
            return ResponseResult.success(clmsTpaSettleInfoMapper.getInvestigateStats(queryVO));
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
    }

    /**
     *  调查看板-未决情况
     */
    @ApiOperation("调查看板-未决情况")
    @PostMapping(value = "/getPendingInvestigateStats", produces = {"application/json"})

    public ResponseResult<Object> getPendingInvestigateStats(@RequestBody TpaHomeworkStatusRequestVo queryVO) {
        try {
            List<String> departmentCodes = taskListService.getAllDepartmentCodesByCode(queryVO.getDepartmentCode());
            queryVO.setDepartmentCodes(departmentCodes);
            return ResponseResult.success(clmsTpaSettleInfoMapper.getPendingInvestigateStats(queryVO));
        } catch (GlobalBusinessException e) {
            return ResponseResult.fail(e.getCode(), e.getMessage());
        }
    }
}
