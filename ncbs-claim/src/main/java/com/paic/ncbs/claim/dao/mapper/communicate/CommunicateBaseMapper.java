package com.paic.ncbs.claim.dao.mapper.communicate;

import com.paic.ncbs.claim.model.vo.communicate.CommunicateBaseVO;
import com.paic.ncbs.claim.model.dto.communicate.CommunicateBaseDTO;
import org.apache.ibatis.annotations.Param;
import org.mybatis.spring.annotation.MapperScan;

import java.util.List;
import java.util.Map;


@MapperScan
public interface CommunicateBaseMapper {

    List<CommunicateBaseDTO> getHistoryCommunicateBaseList(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    CommunicateBaseDTO getCommunicateBaseById(@Param("idAhcsCommunicateBase") String idAhcsCommunicateBase);

    void insertCommunicateBaseDTO(CommunicateBaseDTO communicateBaseDTO);

    void updateCommunicateBaseInfo(CommunicateBaseDTO communicateBaseDTO);

    CommunicateBaseDTO getFreeAppraisalCommunicate(@Param("reportNo") String reportNo, @Param("caseTimes") int caseTimes);

    CommunicateBaseDTO getNotCommunicateBaseDTO(CommunicateBaseVO communicateBaseVO);

    List<CommunicateBaseDTO> getCommunicateBaseInfoByConditions(CommunicateBaseVO communicateBaseVO);

    List<Map<String, String>> getDefaultCommunicatePerson(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("taskDefinitionBpmKey") String taskDefinitionBpmKey,
                                                                 @Param("userId") String userId);

    List<CommunicateBaseDTO> getCommunicateFromNoTaskByConditions(CommunicateBaseVO communicateBaseVO);

    void cancelCommunicateTask(@Param("reportNo") String reportNo, @Param("caseTimes") Integer caseTimes, @Param("userId") String userId);

    CommunicateBaseVO getCommunicateBaseInfo(@Param("reportNo") String reportNo);


}
