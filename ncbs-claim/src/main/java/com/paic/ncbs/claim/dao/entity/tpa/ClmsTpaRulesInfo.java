package com.paic.ncbs.claim.dao.entity.tpa;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * TPA规则表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Getter
@Setter
@TableName("clms_tpa_rules_info")
public class ClmsTpaRulesInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 任务环节号
     */
    @TableField("task_bpm_key")
    private String taskBpmKey;

    /**
     * 环节操作类型
     */
    @TableField("opt_type")
    private String optType;

    /**
     * 是否重开
     */
    @TableField("is_reopened")
    private String isReopened;

    /**
     * 提交人类型
     */
    @TableField("submitter")
    private String submitter;

    /**
     * 差错类型
     */
    @TableField("error_type")
    private String errorType;

    /**
     * 重开类型
     */
    @TableField("reopened_type")
    private String reopenedType;

    /**
     * 重开提交人TPA是否一致
     */
    @TableField("is_tpa_consistent")
    private String isTpaConsistent;

    /**
     * 是否有效，1-是 0：否
     */
    @TableField("is_valid")
    private String isValid;

    /**
     * 无效原因
     */
    @TableField("invalid_reason")
    private String invalidReason;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人员
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private LocalDateTime sysCtime;

    /**
     * 修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private LocalDateTime sysUtime;

    public List<String> getErrorTypeIncludedList() {
        return parseStringToList(errorType);
    }
    public List<String> getReopenedTypeIncludedList() {
        return parseStringToList(reopenedType);
    }

    // 工具方法：将逗号分隔的字符串转为列表
    private List<String> parseStringToList(String str) {
//        if (str == null || str.trim().isEmpty()) {
//            return List.of();
//        }
        return Arrays.stream(str.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }
}
