package com.paic.ncbs.claim.model.vo.restartcase;

import com.paic.ncbs.claim.model.dto.estimate.EstimatePolicyDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2023-05-24 10:06
 */
@Data
public class RestartCaseVO {

    @ApiModelProperty("表主键")
    private String idClmRestartCaseRecord;
    @ApiModelProperty("报案号")
    private String reportNo;
    @ApiModelProperty("赔付次数")
    private Integer caseTimes;
    @ApiModelProperty("重开原因")
    private String restartReason;
    /**
     * 案件类别，一级类别：1 人伤，2 非人伤
     */
    @ApiModelProperty("案件类别")
    private Integer caseType;
    /**
     * 案件类别，二级类别：意外医疗、疾病住院医疗、疾病门急诊医疗、重大疾病、津贴、意外残疾、疾病残疾、意外身故、疾病身故、其他
     */
    @ApiModelProperty("案件类别")
    private List<String> caseKind;
    @ApiModelProperty("重开说明")
    private String restartDescription;

    @ApiModelProperty("意键险保单预估列表")
    private List<EstimatePolicyDTO> estimatePolicyList;

    @ApiModelProperty("重开金额")
    private BigDecimal restartAmount;

}
