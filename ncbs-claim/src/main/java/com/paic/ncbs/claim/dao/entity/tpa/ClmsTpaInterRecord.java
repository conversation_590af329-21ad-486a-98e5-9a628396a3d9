package com.paic.ncbs.claim.dao.entity.tpa;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 理赔与结算平台交互记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Getter
@Setter
@TableName("clms_tpa_inter_record")
public class ClmsTpaInterRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     * 产品
     */
    @TableField("product")
    private String product;

    /**
     * 方案
     */
    @TableField("scheme")
    private String scheme;

    /**
     * 环节
     */
    @TableField("process")
    private String process;

    /**
     * 提交人
     */
    @TableField("submitter")
    private String submitter;

    /**
     * 报案时间
     */
    @TableField("report_time")
    private Date reportTime;

    /**
     * 业务号
     */
    @TableField("business_no")
    private String businessNo;

    /**
     * 接口名称，G-获取，S-推送
     */
    @TableField("request_type")
    private String requestType;

    /**
     * 请求报文
     */
    @TableField("request_info")
    private String requestInfo;

    /**
     * 响应报文
     */
    @TableField("response_info")
    private String responseInfo;

    /**
     * 请求是否成功，Y-成功，N-失败
     */
    @TableField("is_success")
    private String isSuccess;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人员
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private Date sysUtime;
}
