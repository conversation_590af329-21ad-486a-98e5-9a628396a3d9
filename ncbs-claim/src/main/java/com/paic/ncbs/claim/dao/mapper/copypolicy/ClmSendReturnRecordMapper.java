package com.paic.ncbs.claim.dao.mapper.copypolicy;

import com.paic.ncbs.claim.dao.entity.copypolicy.ClmGlobalReturnInfo;
import com.paic.ncbs.claim.dao.entity.copypolicy.ClmSendReturnRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * global回流接口发送记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
public interface ClmSendReturnRecordMapper extends BaseMapper<ClmSendReturnRecord> {

    ClmSendReturnRecord selectByReportNo(@Param("reportNo") String reportNo,@Param("requestType") String requestType);

    void updateByReportNoAndType(ClmSendReturnRecord clmSendReturnRecord);
}
