package com.paic.ncbs.claim.dao.entity.tpa;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * TPA结算信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Getter
@Setter
@TableName("clms_tpa_settle_info")
public class ClmsTpaSettleInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 业务号
     */
    @TableField("business_no")
    private String businessNo;

    /**
     * 报案号
     */
    @TableField("report_no")
    private String reportNo;

    /**
     * 赔付次数
     */
    @TableField("case_times")
    private Integer caseTimes;

    /**
     * 任务环节号
     */
    @TableField("task_bpm_key")
    private String taskBpmKey;

    /**
     * 环节操作类型(1：提交上级，2：退回，3：提交完成)
     */
    @TableField("opt_type")
    private String optType;

    /**
     * 报案时间
     */
    @TableField("report_time")
    private Date reportTime;

    /**
     * 保单号
     */
    @TableField("policy_no")
    private String policyNo;

    /**
     * 提交人
     */
    @TableField("submitter")
    private String submitter;

    /**
     * 所属TPA
     */
    @TableField("submit_tpa")
    private String submitTpa;

    /**
     * 服务号
     */
    @TableField("service_no")
    private String serviceNo;

    /**
     * 费用金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 结案时间
     */
    @TableField("end_case_date")
    private Date endCaseDate;

    /**
     * 结算状态
     */
    @TableField("settle_flag")
    private String settleFlag;

    /**
     * 是否有效，1-是 0：否
     */
    @TableField("is_valid")
    private String isValid;

    /**
     * 无效原因
     */
    @TableField("invalid_reason")
    private String invalidReason;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人员
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("sys_ctime")
    private Date sysCtime;

    /**
     * 修改人员
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 修改时间
     */
    @TableField("sys_utime")
    private Date sysUtime;

    /**
     * 计费方式(0-按单结算 1-按保单保费比例 2-按赔案赔款比例  3-固定金额  4-事后结算)
     */
    @TableField("billing_method")
    private String billingMethod;

    /**
     * 税率
     */
    @TableField("tax_rate")
    private BigDecimal taxRate;

    /**
     * 是否人伤案件(暂定 Y-是，N-否)
     */
    @TableField("is_people_hurt")
    private String isPeopleHurt;

    /**
     * 服务名称
     */
    @TableField("server_name")
    private String serverName;

    /**
     * 服务详情
     */
    @TableField("server_details")
    private String serverDetails;

    @TableField("billing_mode")
    private String billingMode;     // 计费模式(01 全包计费  02 环节计费)
    @TableField("communicate_title")
    private String communicateTitle;    // 沟通主题
    @TableField("valid_period")
    private String validPeriod;     // 有效期
    @TableField("apply_all_products")
    private String applyAllProducts;    // 是否适用于全部产品
}
