package com.paic.ncbs.claim.service.tpa.impl;

import com.alibaba.fastjson.JSON;
import com.paic.ncbs.base.util.MeshSendUtils;
import com.paic.ncbs.claim.common.constant.BaseConstant;
import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.NcbsConstant;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.common.util.StringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.entity.ahcs.AhcsPolicyInfoEntity;
import com.paic.ncbs.claim.dao.entity.endcase.CaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.endcase.WholeCaseBaseEntity;
import com.paic.ncbs.claim.dao.entity.report.ReportInfoEntity;
import com.paic.ncbs.claim.dao.entity.restartcase.RestartCaseRecordEntity;
import com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaInterRecord;
import com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaRulesInfo;
import com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaSettleInfo;
import com.paic.ncbs.claim.dao.mapper.ahcs.AhcsPolicyInfoMapper;
import com.paic.ncbs.claim.dao.mapper.communicate.CommunicateBaseMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.CaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.endcase.WholeCaseBaseMapper;
import com.paic.ncbs.claim.dao.mapper.estimate.EstimatePolicyMapper;
import com.paic.ncbs.claim.dao.mapper.report.PolicyMapper;
import com.paic.ncbs.claim.dao.mapper.report.ReportInfoMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.tpa.ClmsTpaInterRecordMapper;
import com.paic.ncbs.claim.dao.mapper.tpa.ClmsTpaRulesInfoMapper;
import com.paic.ncbs.claim.dao.mapper.tpa.ClmsTpaSettleInfoMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.feign.TpaGlobalFeign;
import com.paic.ncbs.claim.model.dto.communicate.CommunicateBaseDTO;
import com.paic.ncbs.claim.model.dto.investigate.TpaGlobalAgentDTO;
import com.paic.ncbs.claim.model.dto.report.PolicyRiskSubPropDTO;
import com.paic.ncbs.claim.model.dto.tpa.SettlementRule;
import com.paic.ncbs.claim.model.dto.investigate.tpa.SettlePolicyServerUploadListDTO;
import com.paic.ncbs.claim.model.vo.investigate.TpaSupplierInfoListVO;
import com.paic.ncbs.claim.model.vo.report.PolicyQueryVO;
import com.paic.ncbs.claim.service.restartcase.RestartCaseService;
import com.paic.ncbs.claim.service.tpa.TpaSettleInfoService;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.um.model.dto.UserGradeInfoDTO;
import com.paic.ncbs.um.service.CacheService;
import org.bouncycastle.jce.provider.JDKKeyFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * TPA结算信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Service
public class TpaSettleInfoServiceImpl extends ServiceImpl<ClmsTpaSettleInfoMapper, ClmsTpaSettleInfo> implements TpaSettleInfoService {

    @Autowired
    private ClmsTpaSettleInfoMapper clmsTpaSettleInfoMapper;

    @Autowired
    private RestartCaseService restartCaseService;

    @Autowired
    private WholeCaseBaseMapper wholeCaseBaseMapper;

    @Autowired
    private TaskInfoMapper taskInfoMapper;

    @Autowired
    private EstimatePolicyMapper estimatePolicyMapper;

    @Autowired
    private ReportInfoMapper reportInfoMapper;

    @Autowired
    private ClmsTpaRulesInfoMapper clmsTpaRulesInfoMapper;

    @Autowired
    private TpaGlobalFeign tpaGlobalFeign;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private ClmsTpaInterRecordMapper clmsTpaInterRecordMapper;

    @Autowired
    private AhcsPolicyInfoMapper ahcsPolicyInfoMapper;

    @Autowired
    private CaseBaseMapper caseBaseMapper;

    @Autowired
    private CommunicateBaseMapper communicateBaseMapper;

    @Autowired
    private PolicyMapper policyMapper;

    @Value("${ncbs.tpa.url:http://tpa-admin.lb.ssdev.com:48004/admin-api/public/claim/batchInsertDataList}")
    private String tpaUrl;

    @Override
    public void saveTpaSettleInfo(ClmsTpaSettleInfo clmsTpaSettleInfo,String wholeCaseStatus) {
        String assigner = clmsTpaSettleInfo.getSubmitter();     // 提交人
        String reportNo = clmsTpaSettleInfo.getReportNo();      // 报案号
        Integer caseTimes = clmsTpaSettleInfo.getCaseTimes();   // 赔付次数
        String taskDefinitionBpmKey = clmsTpaSettleInfo.getTaskBpmKey(); // 环节
        String optType = clmsTpaSettleInfo.getOptType(); // 环节操作类型(1：提交上级，2：退回，3：提交完成)
        String submitTpa = clmsTpaSettleInfo.getSubmitTpa();
        Date endCaseDate = clmsTpaSettleInfo.getEndCaseDate();
        String policyNo = null; // 保单号
        Date reportDate = null; // 报案时间
        String productCode = null; // 产品代码
        String riskGroupNo = null;  // 方案代码
        String comunicateTitle = null; // 沟通主题
        String validDate = null;     // 有效时间=报案时间

        List<AhcsPolicyInfoEntity> ahcsPolicyInfoEntities = ahcsPolicyInfoMapper.selectByReportNo(reportNo);
        if (!CollectionUtils.isEmpty(ahcsPolicyInfoEntities)) {
            policyNo = ahcsPolicyInfoEntities.get(0).getPolicyNo(); // 保单号
            productCode = ahcsPolicyInfoEntities.get(0).getProductCode(); // 产品代码
        }
        ReportInfoEntity reportInfo = reportInfoMapper.getReportInfo(reportNo);
        if (reportInfo != null) {
            reportDate = reportInfo.getReportDate(); // 报案时间
            if(reportDate!=null){
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                validDate = sdf.format(reportDate);
            }
        }
//        CaseBaseEntity caseBaseInfo = caseBaseMapper.getCaseBaseInfo(reportNo, policyNo, caseTimes);
        PolicyQueryVO policyQueryVO = new PolicyQueryVO();
        policyQueryVO.setPolicyNo(policyNo);
        PolicyRiskSubPropDTO riskGroupNoByPolicyNo = policyMapper.getRiskGroupNoByPolicyNo(policyQueryVO);
        if (riskGroupNoByPolicyNo != null) {
            riskGroupNo = riskGroupNoByPolicyNo.getRiskGroupNo(); // 方案代码
        }
        if (BpmConstants.OC_COMMUNICATE.equals(taskDefinitionBpmKey)) {
            CommunicateBaseDTO oneCommunicate = communicateBaseMapper.getOneCommunicate(reportNo, caseTimes);
            if (oneCommunicate != null) {
                comunicateTitle = oneCommunicate.getCommunicateTitle(); // 沟通主题
            }
        }
        TpaGlobalAgentDTO.TpaRequestData tpaRequestData = getTpaRequestData(assigner, "", submitTpa, productCode, riskGroupNo, validDate, taskDefinitionBpmKey, comunicateTitle);
        TpaSupplierInfoListVO externalDepartmentList = getExternalDepartmentList(clmsTpaSettleInfo, tpaRequestData, BaseConstant.STRING_1);
        List<ClmsTpaSettleInfo> clmsTpaSettleInfoList = new ArrayList<>();
        if (externalDepartmentList != null) {
            List<TpaSupplierInfoListVO.ServerInfo> serverInfoList = externalDepartmentList.getServerInfoList();
            if (!CollectionUtils.isEmpty(serverInfoList)) {
                for (TpaSupplierInfoListVO.ServerInfo serverInfo : serverInfoList) {
                    ClmsTpaSettleInfo tpaSettleInfo = new ClmsTpaSettleInfo();
                    tpaSettleInfo.setId(UuidUtil.getUUID());
                    tpaSettleInfo.setBusinessNo(UuidUtil.getUUID());//业务号
                    tpaSettleInfo.setOptType(optType);
                    tpaSettleInfo.setReportNo(reportNo);
                    tpaSettleInfo.setCaseTimes(caseTimes);
                    tpaSettleInfo.setPolicyNo(policyNo);        // 保单号
                    tpaSettleInfo.setTaskBpmKey(taskDefinitionBpmKey);
                    tpaSettleInfo.setReportTime(reportDate);    // 报案时间
                    tpaSettleInfo.setSubmitter(assigner);
                    tpaSettleInfo.setSubmitTpa(submitTpa);      // 所属TPA（供应商代码）
                    tpaSettleInfo.setServiceNo(serverInfo.getServerCode()); // 服务号
                    tpaSettleInfo.setServerName(serverInfo.getServerName()); // 服务名称
                    tpaSettleInfo.setServerDetails(serverInfo.getServerDetails());  // 服务详情
                    tpaSettleInfo.setAmount(serverInfo.getUnitPrice() == null ? BigDecimal.ZERO : new BigDecimal(serverInfo.getUnitPrice())); //费用金额
                    tpaSettleInfo.setIsPeopleHurt(serverInfo.getPeopleHurtCase()); //是否人伤
                    tpaSettleInfo.setBillingMode(serverInfo.getBillingMode());
                    tpaSettleInfo.setValidPeriod(serverInfo.getValidPeriod());
                    tpaSettleInfo.setCommunicateTitle(serverInfo.getCommunicateTitle());
                    tpaSettleInfo.setApplyAllProducts(serverInfo.getApplyAllProducts());
                    tpaSettleInfo.setEndCaseDate(endCaseDate);          // 结案时间
                    tpaSettleInfo.setSettleFlag(BaseConstant.STRING_0); // 结算状态（0：未结算 1：已结算 2：结算失败）
                    tpaSettleInfo.setIsValid(BaseConstant.STRING_1);    // 是否有效 0：否 1：是
                    Date currentTime = new Date();
                    tpaSettleInfo.setCreatedBy(assigner);
                    tpaSettleInfo.setSysCtime(currentTime);
                    tpaSettleInfo.setSysUtime(currentTime);
                    tpaSettleInfo.setUpdatedBy(assigner);
                    clmsTpaSettleInfoList.add(tpaSettleInfo);
                }
            }
        }
        if(BaseConstant.STRING_0.equals(wholeCaseStatus)){
                clmsTpaSettleInfo.setReportNo(reportNo);
                clmsTpaSettleInfo.setCaseTimes(caseTimes);
                clmsTpaSettleInfo.setEndCaseDate(endCaseDate);
            clmsTpaSettleInfoMapper.updateSettleInfo(clmsTpaSettleInfo);
        }
        clmsTpaSettleInfoMapper.saveSettleInfo(clmsTpaSettleInfoList);
    }

    /**
     * 校验规则并保存服务信息
     * @param clmsTpaSettleInfo
     */
    @Async("asyncPool")
    public void validateRuleAndSaveInfo(ClmsTpaSettleInfo clmsTpaSettleInfo) {
        LogUtil.audit("任务信息入参,clmsTpaSettleInfo={}", JSON.toJSONString(clmsTpaSettleInfo));
        if (clmsTpaSettleInfo != null) {
            String assigner = clmsTpaSettleInfo.getSubmitter();     // 提交人
            String reportNo = clmsTpaSettleInfo.getReportNo();      // 报案号
            Integer caseTimes = clmsTpaSettleInfo.getCaseTimes();   // 赔付次数
            String errorType = ""; // 差错类型
            String reopenedType = ""; // 重开类型
            boolean isReopened = false; // 是否重开
            boolean booleanResult = true; // 是否调结算平台
            String taskDefinitionBpmKey = clmsTpaSettleInfo.getTaskBpmKey(); // 环节
            String wholeCaseStatus = ""; // 整案件状态(0-已结案 1-已报案 2-已理算 6-已归档(农险用))
            String optType = clmsTpaSettleInfo.getOptType(); // 环节操作类型(1：提交上级，2：退回，3：提交完成)
            Date endCaseDate = null;      // 结案时间
            // 重开类型
            WholeCaseBaseEntity wholeCaseBase = wholeCaseBaseMapper.getWholeCaseBaseByReportNoAndCaseTimes(reportNo, caseTimes);
            if (wholeCaseBase != null) {
                //赔付结论20241203更新：1-赔付 2-零结 4-拒赔 5-立案注销 6-报案注销
                reopenedType = wholeCaseBase.getIndemnityConclusion();
                wholeCaseStatus = wholeCaseBase.getWholeCaseStatus();
            }
            // 查询用户岗位是否为TPA岗位（TPA作业岗-新，TPA管理岗-新）
//            boolean isTpaGrade = queryUserGradeIsTpa(assigner);
            if (true) {
                // 查询当前提交人所属TPA的供应商代码
                String submitTpa = isCurrentUserInTPA(clmsTpaSettleInfo);
//                String submitTpa = "TPA";
                if (!"".equals(submitTpa)) {
                    // 查询 是否重开，差错类型，重开类型
                    if (caseTimes > BaseConstant.INT_1) {
                        isReopened = true;
                    }
                    // 差错类型
                    List<RestartCaseRecordEntity> restartCaseRecordEntityList = restartCaseService.getRestartCaseList(reportNo, caseTimes - 1);
                    if (!CollectionUtils.isEmpty(restartCaseRecordEntityList)) {
                        errorType = restartCaseRecordEntityList.get(0).getErrorType();
                    }

                    // 查询TPA是否一致
                    Boolean specialUserInTPA = isSpecialUserInTPA(reportNo, caseTimes, taskDefinitionBpmKey, submitTpa, optType);
                    // 排除报案注销和人伤,是人伤，则案件重开后不调结算平台
                    if (!("6".equals(reopenedType) || (hasPeopleHurtRecord(reportNo) && caseTimes>BaseConstant.INT_1))) {
                        SettlementRule settlementRule = new SettlementRule();
                        settlementRule.setStage(taskDefinitionBpmKey); //环节
                        settlementRule.setIsReopened(isReopened); //是否重开
                        settlementRule.setSubmitType("TPA"); //提交人类型
                        settlementRule.setErrorTypeIncluded(errorType); // 差错类型
                        settlementRule.setReopenedTypeIncluded(reopenedType); // 重开类型
                        settlementRule.setTpaConsistent(specialUserInTPA); //TPA是否一致

                        ClmsTpaRulesInfo clmsTpaRulesInfo = new ClmsTpaRulesInfo();
                        clmsTpaRulesInfo.setTaskBpmKey(taskDefinitionBpmKey);
                        List<ClmsTpaRulesInfo> tpaRules = clmsTpaRulesInfoMapper.getTpaRules(clmsTpaRulesInfo);
                        LogUtil.audit("TPA规则,tpaRules={}", tpaRules);

                        booleanResult = needCallSettlementPlatform(settlementRule, tpaRules);
                        // 环节为【收单提交】，覆盖原收单信息
                        if (BaseConstant.STRING_06.equals(taskDefinitionBpmKey)) {
                            ClmsTpaSettleInfo tpaSettle = new ClmsTpaSettleInfo();
                            tpaSettle.setReportNo(clmsTpaSettleInfo.getReportNo());
                            tpaSettle.setCaseTimes(clmsTpaSettleInfo.getCaseTimes());
                            tpaSettle.setTaskBpmKey(BaseConstant.STRING_06);
                            tpaSettle.setOptType(clmsTpaSettleInfo.getOptType());
                            delSettleInfo(clmsTpaSettleInfo);
                        }
                        // 是否调用结算平台（true=调用，false=不调用）
                        if (booleanResult) {
                            if (wholeCaseBase != null) {
                                endCaseDate = wholeCaseBase.getEndCaseDate(); // 结案时间
                            }
                            clmsTpaSettleInfo.setEndCaseDate(endCaseDate);
                            clmsTpaSettleInfo.setSubmitTpa(submitTpa);
                            // 调用结算平台,保存服务信息
                            saveTpaSettleInfo(clmsTpaSettleInfo,wholeCaseStatus);
                        }
                        // 服务数据导入
                        settleInfoServerUpload(reportNo, caseTimes, assigner);
                    }
                }
            }
            if(BaseConstant.STRING_0.equals(wholeCaseStatus)){
                clmsTpaSettleInfo.setReportNo(reportNo);
                clmsTpaSettleInfo.setCaseTimes(caseTimes);
                clmsTpaSettleInfo.setEndCaseDate(endCaseDate);
                clmsTpaSettleInfoMapper.updateSettleInfo(clmsTpaSettleInfo);
            }
        }
    }

    @Override
    @Async("asyncPool")
    public void delSettleInfo(ClmsTpaSettleInfo tpaSettleInfo) {
        LogUtil.audit("tpaSettleInfo： {}", JsonUtils.toJsonString(tpaSettleInfo));
        clmsTpaSettleInfoMapper.delSettleInfo(tpaSettleInfo);
    }

    /**
     * 查询当前提交人所属TPA的供应商代码
     * @param clmsTpaSettleInfo
     */
    private String isCurrentUserInTPA(ClmsTpaSettleInfo clmsTpaSettleInfo){
        TpaGlobalAgentDTO.TpaRequestData tpaRequestData = getTpaRequestData(clmsTpaSettleInfo.getSubmitter(), BaseConstant.STRING_04, "", "", "", "", "", "");
        TpaSupplierInfoListVO externalDepartmentList = getExternalDepartmentList(clmsTpaSettleInfo,tpaRequestData,BaseConstant.STRING_0);
        String submitTpa = "";
        if(externalDepartmentList != null){
            if(!externalDepartmentList.getSupplierInfoList().isEmpty()){
                TpaSupplierInfoListVO.SupplierInfo supplierInfo = externalDepartmentList.getSupplierInfoList().get(0);
                submitTpa = supplierInfo.getSupplierCode();
            }
        }
        return submitTpa;
    }
    /**
     * 校验特殊情况TPA是否一致
     * @param reportNo
     * @param caseTimes
     * @param taskDefinitionBpmKey
     * @param submitTpa
     */
    private Boolean isSpecialUserInTPA(String reportNo,Integer caseTimes,String taskDefinitionBpmKey,String submitTpa, String optType){
        boolean isSpecialUserInTPA = false;
        if(caseTimes>1){
            ClmsTpaSettleInfo clmsTpaSettleInfo = new ClmsTpaSettleInfo();
            clmsTpaSettleInfo.setReportNo(reportNo);
            clmsTpaSettleInfo.setTaskBpmKey(taskDefinitionBpmKey);
            clmsTpaSettleInfo.setCaseTimes(caseTimes-1);
            clmsTpaSettleInfo.setOptType(optType);

            List<ClmsTpaSettleInfo> clmsTpaSettleInfos = clmsTpaSettleInfoMapper.selectTpaSettleInfo(clmsTpaSettleInfo);
            LogUtil.audit("clmsTpaSettleInfos的值： {}", JsonUtils.toJsonString(clmsTpaSettleInfos));
            if(!CollectionUtils.isEmpty(clmsTpaSettleInfos)){
                for (ClmsTpaSettleInfo tpaSettleInfo : clmsTpaSettleInfos) {
                    if (submitTpa.equals(tpaSettleInfo.getSubmitTpa())) {
                        isSpecialUserInTPA = true;
                        break;
                    }
                }
            }else{
                return isSpecialUserInTPA;
            }
        }else{
            return isSpecialUserInTPA;
        }
        return isSpecialUserInTPA;
    }


    /**
     * 判断是否需要调用结算平台
     * 逻辑：只要有一条“不调用”的规则匹配当前场景，则返回false（不调用）；否则返回true（调用）
     * @param settlementRule 当前业务场景信息
     * @param allRules 所有“不调用结算平台”的规则
     * @return 是否调用结算平台（true=调用，false=不调用）
     */
    public static boolean needCallSettlementPlatform(SettlementRule settlementRule, List<ClmsTpaRulesInfo> allRules) {
        // 过滤出与当前环节相关的规则
        List<ClmsTpaRulesInfo> candidateRules = allRules.stream()
                .filter(rule -> isStageMatch(settlementRule.getStage(), rule.getTaskBpmKey()))
                .collect(Collectors.toList());
        if (candidateRules.isEmpty()) {
            return true; // 无相关规则，默认调用
        }
        // 逐个匹配规则，只要有一条匹配则不调用
        for (ClmsTpaRulesInfo rule : candidateRules) {
            boolean ruleMatch = isRuleMatch(settlementRule, rule);
            if (ruleMatch) {
                return false; // 匹配到“不调用”的规则，返回false
            }
        }
        // 无任何规则匹配，默认调用
        return true;
    }

    /**
     * 判断当前场景的环节是否与规则中的环节匹配
     * @param sceneStage 当前场景的环节
     * @param ruleStage 规则中定义的环节
     * @return 是否匹配
     */
    private static boolean isStageMatch(String sceneStage, String ruleStage) {
        // 如果规则中未指定环节（null或空），则认为匹配所有环节
        if (ruleStage == null || ruleStage.trim().isEmpty()) {
            return true;
        }
        // 如果当前场景没有环节信息，且规则指定了环节，则不匹配
        if (sceneStage == null || sceneStage.trim().isEmpty()) {
            return false;
        }
        return sceneStage.equals(ruleStage);
    }

    /**
     * 检查单条规则是否匹配当前场景
     */
    private static boolean isRuleMatch(SettlementRule settlementRule, ClmsTpaRulesInfo rule) {
        // 检查“是否重开”是否匹配
        boolean booleanMatch = isBooleanMatch(settlementRule.getIsReopened(), rule.getIsReopened().equals(BaseConstant.STRING_1));
        if (!booleanMatch) {
            return false;
        }
        // 检查“重开差错类型”是否匹配（包含/排除逻辑）
        boolean errorTypeMatch = isErrorTypeMatch(settlementRule.getErrorTypeIncluded(), rule);
        if (!errorTypeMatch) {
            return false;
        }
        // 检查“重开类型”是否匹配（包含/排除逻辑）
        boolean reopenedTypeMatch = isReopenedTypeMatch(settlementRule.getReopenedTypeIncluded(), rule);
        if (!reopenedTypeMatch) {
            return false;
        }
        // 检查“TPA一致性”是否匹配
        boolean booleanMatchTPA = isBooleanMatch(settlementRule.getTpaConsistent(), rule.getIsTpaConsistent().equals(BaseConstant.STRING_1));
        if (!booleanMatchTPA) {
            return false;
        }
        // 检查“提交人类型”是否匹配
        boolean stringMatch = isStringMatch(settlementRule.getSubmitType(), rule.getSubmitter());
        if (!stringMatch) {
            return false;
        }
        // 所有条件均匹配
        return true;
    }

    // 辅助方法：布尔值匹配（null表示不限制）
    private static boolean isBooleanMatch(Boolean sceneValue, Boolean ruleValue) {
        return ruleValue == null || (sceneValue != null && sceneValue.equals(ruleValue));
    }

    // 辅助方法：字符串匹配（null或空表示不限制）
    private static boolean isStringMatch(String sceneValue, String ruleValue) {
        if (ruleValue == null || ruleValue.trim().isEmpty()) {
            return true; // 规则不限制该条件
        }
        return sceneValue != null && sceneValue.equals(ruleValue);
    }

    // 辅助方法：差错类型,重开类型匹配（包含/排除逻辑）
    private static boolean isErrorTypeMatch(String errorType, ClmsTpaRulesInfo rule) {
        // 检查“包含”条件：如果规则指定了包含的差错类型，必须命中其中一个
        List<String> included = rule.getErrorTypeIncludedList();
        if (!included.isEmpty()) {
            if (errorType == null || !included.contains(errorType)) {
                return false; // 未命中包含的类型
            }
        }
        return true;
    }

    private static boolean isReopenedTypeMatch(String reopenedType, ClmsTpaRulesInfo rule) {
        // 检查“包含”条件：如果规则指定了包含的差错类型，必须命中其中一个
        List<String> reopenedTypeList = rule.getReopenedTypeIncludedList();
        if (!reopenedTypeList.isEmpty()) {
            if (reopenedType == null || !reopenedTypeList.contains(reopenedType)) {
                return false;
            }
        }
        return true;
    }


    /**
     * 查询TPA相关接口
     * @param clmsTpaSettleInfo tpa规则信息
     * @param type 0：供应商查询接口  1：服务信息查询接口
     */

    public TpaSupplierInfoListVO getExternalDepartmentList(ClmsTpaSettleInfo clmsTpaSettleInfo, TpaGlobalAgentDTO.TpaRequestData tpaRequestData, String type) {
        TpaGlobalAgentDTO tpaGlobalAgentDTO = new TpaGlobalAgentDTO();
        tpaGlobalAgentDTO.setRequestData(tpaRequestData);
        tpaGlobalAgentDTO.setRequestTime(""+System.currentTimeMillis());
        tpaGlobalAgentDTO.setRequestId(MDC.get(BaseConstant.REQUEST_ID));
        tpaGlobalAgentDTO.setCompanyId("tpa-dev");
        String requestType= null;
        String submitter = clmsTpaSettleInfo.getSubmitter();
        String reportNo = clmsTpaSettleInfo.getReportNo();
        Integer caseTimes = clmsTpaSettleInfo.getCaseTimes();
        String isSuccess = "";
        if(BaseConstant.STRING_0.equals(type)){
            requestType ="FST-008";
        }else if(BaseConstant.STRING_1.equals(type)){
            requestType ="FST-002";
        }
        tpaGlobalAgentDTO.setRequestType(requestType);
        // 保存交互记录
        ClmsTpaInterRecord clmsTpaInterRecord = new ClmsTpaInterRecord();
        clmsTpaInterRecord.setId(UuidUtil.getUUID());
        clmsTpaInterRecord.setReportNo(reportNo);
        clmsTpaInterRecord.setCaseTimes(caseTimes);
        clmsTpaInterRecord.setSubmitter(submitter);
        clmsTpaInterRecord.setRequestInfo(JsonUtils.toJsonString(tpaGlobalAgentDTO));
        clmsTpaInterRecord.setRequestType(requestType);
        Date currentTime = new Date();
        clmsTpaInterRecord.setCreatedBy(submitter);
        clmsTpaInterRecord.setSysCtime(currentTime);
        clmsTpaInterRecord.setSysUtime(currentTime);
        clmsTpaInterRecord.setUpdatedBy(submitter);
        try {
            String result = null;

            result = tpaGlobalFeign.getExternalDepartmentList(tpaGlobalAgentDTO);
//            result = "{\n" +
//                    "    \"requestType\": \"FST-002\",\n" +
//                    "    \"requestId\": \"7AA6A7CCFA1E488DA258D77BB6D4E56B\",\n" +
//                    "    \"responseTime\": \"20240614110458001\",\n" +
//                    "    \"companyId\": \"tpa-dev\",\n" +
//                    "    \"resultCode\": \"000000\",\n" +
//                    "    \"resultMsg\": \"消息处理成功\",\n" +
//                    "    \"responseData\": {\n" +
//                    "        \"serverInfoList\": [\n" +
//                    "            {\n" +
//                    "                \"serverCode\": \"00001\",\n" +
//                    "                \"supplierCode\": \"333444\",\n" +
//                    "                \"serverName\": \"测试服务01\",\n" +
//                    "                \"billingMethod\": \"1\",\n" +
//                    "                \"unitPrice\": \"100.00\",\n" +
//                    "                \"taxRate\": \"10\",\n" +
//                    "                \"peopleHurtCase\": \"1\",\n" +
//                    "                \"serverDetails\": \"服务详情01\",\n" +
//                    "                \"billingMode\": \"01\",\n" +
//                    "                \"communicateTitle\": \"02\",\n" +
//                    "                \"validPeriod\": \"111\",\n" +
//                    "                \"applyAllProducts\": \"0\"\n" +
//                    "            }\n" +
//                    "        ]\n" +
//                    "    }\n" +
//                    "}";
            Map<String, Object> map = JsonUtils.jsonToMap(result);
            clmsTpaInterRecord.setResponseInfo(result);
            if("000000".equals(String.valueOf(map.get("resultCode")))){
                isSuccess = BaseConstant.STRING_1;
            }else{
                isSuccess = BaseConstant.STRING_0;
            }
            clmsTpaInterRecord.setIsSuccess(isSuccess);
            clmsTpaInterRecordMapper.insert(clmsTpaInterRecord);
            if(StringUtils.isEmptyStr(result)){
                return null;
            }
            if("000000".equals(String.valueOf(map.get("resultCode")))){
                String responseData = JsonUtils.toJsonString(map.get("responseData"));
                return JsonUtils.toObject(responseData, TpaSupplierInfoListVO.class);
            }else{
                return null;
            }
        }catch (Exception e){
            clmsTpaInterRecord.setIsSuccess(BaseConstant.STRING_0);
            clmsTpaInterRecord.setResponseInfo(e.getMessage());
            clmsTpaInterRecordMapper.insert(clmsTpaInterRecord);
            // 请求失败添加一条服务号为空的数据(重开需要匹配上次一次提交人所属TPA，防止接口失败没有数据)
            createDefaultServiceRecord(clmsTpaSettleInfo,e.getMessage());
            return null;
        }

    }
    private TpaGlobalAgentDTO.TpaRequestData getTpaRequestData(String userId,String supplierTypeCode,String submitTpa, String productCode
                                                    ,String riskGroupNo,String uservalidDateId, String taskDefinitionBpmKey,String comunicateTitle) {
        TpaGlobalAgentDTO tpaGlobalAgentDTO = new TpaGlobalAgentDTO();
        TpaGlobalAgentDTO.TpaRequestData requestData = tpaGlobalAgentDTO.createTpaRequestData();
        requestData.setUserId(userId);                     // 作业人员账号
        requestData.setSupplierTypeCode(supplierTypeCode); // 供应商类型
        requestData.setSupplierCode(submitTpa);            // 供应商代码
        requestData.setProductCode(productCode);           // 产品代码
        requestData.setPackageCode(riskGroupNo);           // 方案代码
        requestData.setValidDate(uservalidDateId);   // 有效时间
        requestData.setTaskNode(taskDefinitionBpmKey);     // 环节
        requestData.setCommunicateTitle(comunicateTitle);   // 沟通主题
        requestData.setServerTypeCode(BaseConstant.STRING_0); // 服务类型代码
        return requestData;
    }

    /**
     * 服务数据导入接口
     * @param reportNo 保单号
     * @param caseTimes 赔付次数
     */
//    @Async("asyncPool")
    public void settleInfoServerUpload(String reportNo,Integer caseTimes,String userId) {
        try {
            //判断案件是否结案，是则服务数据导入
            WholeCaseBaseEntity wholeCaseBase = wholeCaseBaseMapper.getWholeCaseBaseByReportNoAndCaseTimes(reportNo, caseTimes);
            if(wholeCaseBase != null){
                if(BaseConstant.STRING_0.equals(wholeCaseBase.getWholeCaseStatus())){
                    // 查询有异常生成的默认服务数据
                    ClmsTpaSettleInfo tpaSettleInfo = new ClmsTpaSettleInfo();
                    tpaSettleInfo.setReportNo(reportNo);
                    tpaSettleInfo.setCaseTimes(caseTimes);
                    tpaSettleInfo.setIsValid(BaseConstant.STRING_2);
                    List<ClmsTpaSettleInfo> tpaSettleInfos = clmsTpaSettleInfoMapper.selectTpaSettleInfo(tpaSettleInfo);
                    if(!CollectionUtils.isEmpty(tpaSettleInfos)){
                        // 重新查询供应商接口和服务接口
                        for(ClmsTpaSettleInfo clmsTpaSettleInfo : tpaSettleInfos){
                            String currentUserInTPA = isCurrentUserInTPA(clmsTpaSettleInfo);
                            clmsTpaSettleInfo.setSubmitTpa(currentUserInTPA);
                            clmsTpaSettleInfo.setEndCaseDate(wholeCaseBase.getEndCaseDate());
                            saveTpaSettleInfo(clmsTpaSettleInfo,BaseConstant.STRING_0);
                        }
                    }
                    ClmsTpaSettleInfo clmsTpaSettleInfo = new ClmsTpaSettleInfo();
                    clmsTpaSettleInfo.setReportNo(reportNo);
                    clmsTpaSettleInfo.setCaseTimes(caseTimes);
                    clmsTpaSettleInfo.setSettleFlag(BaseConstant.STRING_0); // 未结算
                    clmsTpaSettleInfo.setIsValid(BaseConstant.STRING_1);
                    List<ClmsTpaSettleInfo> clmsTpaSettleInfos = clmsTpaSettleInfoMapper.selectTpaSettleInfo(clmsTpaSettleInfo);
                    List<SettlePolicyServerUploadListDTO.SettlePolicyServerUploadVo> dataList = getSettlePolicyServerUploadVos(clmsTpaSettleInfos);
                    LogUtil.audit("服务导入数据：{}",JsonUtils.toJsonString(dataList));
                    // 请求服务数据导入接口
                String result = MeshSendUtils.post(tpaUrl, JSON.toJSONString(dataList));
//                    String result = "{\n" +
//                            "    \"responseCode\": \"000000\",\n" +
//                            "    \"responseMsg\": \"成功\",\n" +
//                            "    \"data\": null\n" +
//                            "}";
                    Map<String, Object> map = JsonUtils.jsonToMap(result);
                    // 保存交互记录
                    ClmsTpaInterRecord clmsTpaInterRecord = new ClmsTpaInterRecord();
                    clmsTpaInterRecord.setId(UuidUtil.getUUID());
                    clmsTpaInterRecord.setReportNo(reportNo);
                    clmsTpaInterRecord.setCaseTimes(caseTimes);
                    clmsTpaInterRecord.setSubmitter(userId);
                    clmsTpaInterRecord.setRequestInfo(JsonUtils.toJsonString(dataList));
                    clmsTpaInterRecord.setResponseInfo(result);
                    clmsTpaInterRecord.setRequestType("S");
                    Date currentTime = new Date();
                    clmsTpaInterRecord.setCreatedBy(userId);
                    clmsTpaInterRecord.setSysCtime(currentTime);
                    clmsTpaInterRecord.setSysUtime(currentTime);
                    clmsTpaInterRecord.setUpdatedBy(userId);
                    String isSuccess = "";
                    if("000000".equals(String.valueOf(map.get("responseCode")))){
                        isSuccess = BaseConstant.STRING_1;
                        // 服务数据导入成功后修改状态
                        clmsTpaSettleInfo.setSettleFlag(BaseConstant.STRING_1); // 未结算
                        clmsTpaSettleInfoMapper.updateSettleStatus(clmsTpaSettleInfo);
                    }else{
                        isSuccess = BaseConstant.STRING_0;
                    }
                    clmsTpaInterRecord.setIsSuccess(isSuccess);
                    clmsTpaInterRecordMapper.insert(clmsTpaInterRecord);
                }
            }
        }catch (Exception e){
            throw new GlobalBusinessException(e.getMessage());
        }
    }

    private List<SettlePolicyServerUploadListDTO.SettlePolicyServerUploadVo> getSettlePolicyServerUploadVos(List<ClmsTpaSettleInfo> clmsTpaSettleInfos) {
        SettlePolicyServerUploadListDTO settlePolicyServerUploadListDTO = new SettlePolicyServerUploadListDTO();
        List<SettlePolicyServerUploadListDTO.SettlePolicyServerUploadVo> dataList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(clmsTpaSettleInfos)) {
            for (ClmsTpaSettleInfo tpaSettleInfo : clmsTpaSettleInfos) {
                SettlePolicyServerUploadListDTO.SettlePolicyServerUploadVo settlePolicyServerUploadVo = settlePolicyServerUploadListDTO.createSettleInfo();
                settlePolicyServerUploadVo.setBusinessNo(tpaSettleInfo.getBusinessNo()); // 业务号
                settlePolicyServerUploadVo.setBusinessType("O");// 业务类型(P-保全,L-理赔,C-承保 O-其他)
                settlePolicyServerUploadVo.setPolicyNo(tpaSettleInfo.getPolicyNo());  // 保单号
                settlePolicyServerUploadVo.setServerCode(tpaSettleInfo.getServiceNo());// 服务代码
                settlePolicyServerUploadVo.setServerName(tpaSettleInfo.getServerName()); // 服务名称
                settlePolicyServerUploadVo.setServerNumber(BaseConstant.STRING_1); // 服务份数
                settlePolicyServerUploadVo.setServerProductDetails(tpaSettleInfo.getServerDetails()); // 服务详情
                settlePolicyServerUploadVo.setReportNo(tpaSettleInfo.getReportNo()); // 报案号
                dataList.add(settlePolicyServerUploadVo);
            }
        }
        return dataList;
    }

    /**
     * 查询用户岗位是否为TPA
     * @param userId 用户
     */
    public boolean queryUserGradeIsTpa (String userId) {
        boolean result = false;
        try {
            List<UserGradeInfoDTO> userGradeList = cacheService.queryUserGradeList(userId, WebServletContext.getDepartmentCode());
            if(!CollectionUtils.isEmpty(userGradeList)){
                for(UserGradeInfoDTO userGradeInfoDTO : userGradeList){
                    if(NcbsConstant.TPA_MANAGER_GRADE_NAME_NEW.equals(userGradeInfoDTO.getGradeName())
                       || NcbsConstant.TPA_OPERATOR_GRADE_NAME_NEW.equals(userGradeInfoDTO.getGradeName())){
                        result = true;
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取岗位信息异常", e);
        }
        return result;
    }

    /**
     * 检查是否存在人伤记录
     */
    private boolean hasPeopleHurtRecord(String reportNo) {
        ClmsTpaSettleInfo queryCondition = new ClmsTpaSettleInfo();
        queryCondition.setReportNo(reportNo);
        queryCondition.setIsPeopleHurt("Y");
        queryCondition.setIsValid(BaseConstant.STRING_1);
        List<ClmsTpaSettleInfo> records = clmsTpaSettleInfoMapper.selectTpaSettleInfo(queryCondition);
        return !CollectionUtils.isEmpty(records);
    }

    /**
     * 当接口调用失败时，创建默认服务记录
     */
    private void createDefaultServiceRecord(ClmsTpaSettleInfo settleInfo, String errorMsg) {
        try {
            List<ClmsTpaSettleInfo> clmsTpaSettleInfoList = new ArrayList<>();
            settleInfo.setId(UuidUtil.getUUID());
            settleInfo.setBusinessNo(UuidUtil.getUUID());
            settleInfo.setIsValid(BaseConstant.STRING_2); // 2表示异常默认记录，区别于正常记录(1)和无效记录(0)
            settleInfo.setRemark(errorMsg);
            Date currentTime = new Date();
            settleInfo.setCreatedBy(settleInfo.getSubmitter());
            settleInfo.setSysCtime(currentTime);
            settleInfo.setSysUtime(currentTime);
            settleInfo.setUpdatedBy(settleInfo.getSubmitter());
            clmsTpaSettleInfoList.add(settleInfo);
            clmsTpaSettleInfoMapper.saveSettleInfo(clmsTpaSettleInfoList);
            LogUtil.audit("接口调用失败，已为案件{}创建默认服务记录", settleInfo.getReportNo());

        } catch (Exception e) {
            LogUtil.error("创建默认服务记录失败", e);
        }
    }
}
