package com.paic.ncbs.claim.service.entrustment.impl;

import com.paic.ncbs.claim.common.constant.BpmConstants;
import com.paic.ncbs.claim.common.constant.ConstValues;
import com.paic.ncbs.claim.common.constant.Constants;
import com.paic.ncbs.claim.common.constant.InvestigateConstants;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.common.response.ResponseResult;
import com.paic.ncbs.claim.common.util.ListUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentAuditMapper;
import com.paic.ncbs.claim.dao.mapper.entrustment.EntrustmentMapper;
import com.paic.ncbs.claim.dao.mapper.investigate.InvestigateMapper;
import com.paic.ncbs.claim.dao.mapper.taskdeal.TaskInfoMapper;
import com.paic.ncbs.claim.dao.mapper.user.DepartmentDefineMapper;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.accident.AccidentSceneDto;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustAuditDTO;
import com.paic.ncbs.claim.model.dto.entrustment.EntrustMainDTO;
import com.paic.ncbs.claim.model.dto.investigate.InvestigateTaskDTO;
import com.paic.ncbs.claim.model.dto.print.PrintEntrustDTO;
import com.paic.ncbs.claim.model.dto.taskdeal.TaskInfoDTO;
import com.paic.ncbs.claim.model.vo.entrustment.EntrustmentApiVo;
import com.paic.ncbs.claim.model.vo.investigate.ServerInfoVO;
import com.paic.ncbs.claim.model.vo.investigate.TpaServerInfoListVO;
import com.paic.ncbs.claim.model.vo.taskdeal.TaskInfoVO;
import com.paic.ncbs.claim.service.bpm.BpmService;
import com.paic.ncbs.claim.service.common.IOperationRecordService;
import com.paic.ncbs.claim.service.doc.PrintCoreService;
import com.paic.ncbs.claim.service.entrustment.EntrustmentService;
import com.paic.ncbs.claim.service.investigate.InvestigateService;
import com.paic.ncbs.claim.service.notice.NoticeService;
import com.paic.ncbs.claim.service.taskdeal.TaskPoolService;
import com.paic.ncbs.claim.model.dto.notice.NoticesDTO;
import com.paic.ncbs.claim.common.util.LogUtil;
import com.paic.ncbs.claim.utils.JsonUtils;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service("entrustmentService")
public class EntrustmentServiceImpl implements EntrustmentService {

    @Autowired
    private EntrustmentMapper entrustmentMapper;
    @Autowired
    private EntrustmentAuditMapper entrustmentAuditMapper;
    @Autowired
    private NoticeService noticeService;
    @Autowired
    private BpmService bpmService;
    @Autowired
    private IOperationRecordService operationRecordService;
    @Autowired
    InvestigateMapper investigateMapper;
    @Autowired
    private TaskInfoMapper taskInfoMapper;
    @Autowired
    private InvestigateService investigateService;
    @Autowired
    private DepartmentDefineMapper departmentDefineMapper ;
    @Autowired
    private PrintCoreService printCoreService;
    @Autowired
    private TaskPoolService taskPoolService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseResult<Object> saveEntrustment(EntrustmentApiVo entrustmentApiVo) throws GlobalBusinessException {
        LogUtil.info("开始保存第三方委托" + JsonUtils.toJsonString(entrustmentApiVo));
        UserInfoDTO u = WebServletContext.getUser();
        EntrustMainDTO entrustMainDTO = entrustmentApiVo.getEntrustMainDTO();
        // 检查是否可以发起委托
        checkIsCanSendEntrustment(entrustmentApiVo);

        String entrustId = UuidUtil.getUUID();
        List<EntrustMainDTO> firstEntrustmentList = entrustmentMapper.selectByConditions(
            entrustMainDTO.getReportNo(), entrustMainDTO.getCaseTimes(), null, null, "DESC", true);
        EntrustMainDTO existingEntrustment = firstEntrustmentList.isEmpty() ? null : firstEntrustmentList.get(0);
        
        if (existingEntrustment != null) {
            String entrustStatus = existingEntrustment.getEntrustStatus();
            // 0-草稿、1-待审批、2-不同意、3-同意
            if ("0".equals(entrustStatus)) {
                entrustMainDTO.setIdEntrust(existingEntrustment.getIdEntrust());
                entrustMainDTO.setEntrustStatus(entrustmentApiVo.getSubmitFlag());
                entrustMainDTO.setSysUtime(new Date());
                entrustmentMapper.updateEntrustment(entrustMainDTO);
            } else if ("2".equals(entrustStatus) || "3".equals(entrustStatus)) {
                entrustMainDTO.setIdEntrust(entrustId);
                createNewEntrustment(entrustMainDTO, entrustId, entrustmentApiVo.getSubmitFlag(), u);
                entrustmentMapper.insertEntrustment(entrustMainDTO);
            } else if ("1".equals(entrustStatus)) {
                throw new GlobalBusinessException("有正在审批中的委托，请不要重复提交");
            }
        } else {
            createNewEntrustment(entrustMainDTO, entrustId, entrustmentApiVo.getSubmitFlag(), u);
            entrustmentMapper.insertEntrustment(entrustMainDTO);
        }

        if("1".equals(entrustmentApiVo.getSubmitFlag())){
            // 当为提交操作时，生成一笔审批任务
            String entrustAuditId = generateEntrustmentAuditTask(entrustMainDTO, u);
            // 启动审批流程
            bpmService.startProcessEntrustment(entrustMainDTO, BpmConstants.OC_ENTRUSTMENT_APPROVAL,entrustAuditId,u);
            // 记录操作日志
            operationRecordService.insertOperationRecord(entrustMainDTO.getReportNo(), BpmConstants.OC_ENTRUSTMENT_APPROVAL, "发起", null, u.getUserCode());
        }
        return ResponseResult.success();
    }

    @Override
    public ResponseResult<EntrustmentApiVo> getEntrustData(EntrustmentApiVo entrustmentApiVo) {
        LogUtil.info("#委托· 查询委托数据#入参#entrustment=" + JsonUtils.toJsonString(entrustmentApiVo));

        // 获取报案号和赔付次数
        String reportNo = entrustmentApiVo.getReportNo();
        Integer caseTimes = entrustmentApiVo.getCaseTimes();
        String idEntrust = entrustmentApiVo.getIdEntrust();

        EntrustmentApiVo resultVo = new EntrustmentApiVo();

        // 查询当前委托数据
        EntrustMainDTO entrustMain = null;
        if (StringUtils.hasText(idEntrust)) {
            // 根据委托ID查询
            List<EntrustMainDTO> results = entrustmentMapper.selectByConditions(reportNo, caseTimes, idEntrust, null, "DESC", true);
            entrustMain = results.isEmpty() ? null : results.get(0);
        } else {
            // 查询草稿状态的委托数据
            List<EntrustMainDTO> results = entrustmentMapper.selectByConditions(reportNo, caseTimes, null, "0", "DESC", true);
            entrustMain = results.isEmpty() ? null : results.get(0);
        }

        // 设置页面权限判断
        boolean isEditable = determinePagePermission(entrustMain);
        resultVo.setIsEditable(isEditable);

        // 获取历史所有委托数据
        List<EntrustMainDTO> historyEntrustments = entrustmentMapper.selectHistoryByReportNo(reportNo);

        // 转换thirdPartyType为thirdPartyTypeName
        convertThirdPartyTypeName(entrustMain);
        historyEntrustments.forEach(this::convertThirdPartyTypeName);

        String initFlag = entrustmentApiVo.getInitFlag();
        if ("1".equals(initFlag)) {// 录入
            List<UserInfoDTO> approvalUsers = getApprovalUsers(reportNo,caseTimes);

            // 匹配审批人信息
            if (entrustMain != null && StringUtils.hasText(entrustMain.getAuditorCode()) && ListUtils.isNotEmpty(approvalUsers)) {
                for (UserInfoDTO user : approvalUsers) {
                    if (entrustMain.getAuditorCode().equals(user.getUserCode())) {
                        entrustMain.setAuditorUmName(user.getUserName());
                        entrustMain.setAuditorDpmCode(user.getComCode());
                        entrustMain.setAuditorDpmName(user.getComName());
                        break;
                    }
                }
            }

            resultVo.setEntrustMainDTO(entrustMain);
            resultVo.setEntrustMainList(historyEntrustments);

            // 查询当前审批记录
            List<EntrustAuditDTO> currentAuditList = new ArrayList<>();
            if (entrustMain != null && StringUtils.hasText(entrustMain.getIdEntrust())) {
                currentAuditList = entrustmentAuditMapper.selectCurrentAuditByEntrustId(entrustMain.getIdEntrust());
            }
            resultVo.setAuditHistoryList(currentAuditList);
        } else if ("2".equals(initFlag)) {// 审核
            if (entrustMain != null && StringUtils.hasText(entrustMain.getAccidentCode())){
                List<String> itemNames = Arrays.asList(entrustMain.getAccidentCode().split(","));
                Collections.sort(itemNames);
                String accidentName = investigateMapper.getSelectItemName(itemNames);
                entrustMain.setAccidentName(accidentName);
                for (String e : itemNames) {
                    if (ConstValues.ENTRUSTMENT_OTHER.contains(e)) {
                        entrustMain.setAccidentName(accidentName + " " + entrustMain.getOther());
                        break;
                    }
                }
            }
            resultVo.setEntrustMainDTO(entrustMain);
            resultVo.setEntrustMainList(historyEntrustments);
        }

        return ResponseResult.success(resultVo);
    }



    /**
     * 判断页面权限
     */
    private boolean determinePagePermission(EntrustMainDTO currentEntrustment) {
        if (currentEntrustment == null) {
            // 没有委托任务，可读写
            return true;
        }

        String entrustStatus = currentEntrustment.getEntrustStatus();
        UserInfoDTO currentUser = WebServletContext.getUser();
        String currentUserCode = currentUser != null ? currentUser.getUserCode() : null;
        String processorUserCode = currentEntrustment.getCreatedBy();

        switch (entrustStatus) {
            case "0": // 草稿
                return currentUserCode != null && currentUserCode.equals(processorUserCode);
            case "1": // 待审批
                return false;
            case "2": // 不同意
            case "3": // 同意
                return true;
            default:
                return true;
        }
    }

    @Override
    public void checkIsCanSendEntrustment(EntrustmentApiVo apiVo) throws GlobalBusinessException {
        if (!StringUtils.hasText(apiVo.getReportNo())) {
            throw new GlobalBusinessException("报案号不能为空");
        }
        if (apiVo.getCaseTimes() == null) {
            throw new GlobalBusinessException("赔付次数不能为空");
        }

        if (apiVo.getEntrustMainDTO() != null &&
            StringUtils.hasText(apiVo.getEntrustMainDTO().getAuditorCode())) {
            UserInfoDTO currentUser = WebServletContext.getUser();
            if (currentUser != null &&
                apiVo.getEntrustMainDTO().getAuditorCode().equals(currentUser.getUserCode())) {
                throw new GlobalBusinessException("委托审批人不能选择自己");
            }
        }
    }

    @Override
    public List<AccidentSceneDto> getAccidentSceneData(String collectionCode) {
        List<AccidentSceneDto> list = new ArrayList<>();
        String[] codes = collectionCode.split(",");
        for (String code : codes) {
            List<AccidentSceneDto> temp = investigateMapper.getAccidentSceneData(code);
            list.addAll(temp);
        }
        // 只保留 财产险ASM_1005，责任险ASM_1006，货运险ASM_1007，其他ASM_1008
        list.removeIf(item -> !"ASM_1005".equals(item.getValueCode()) 
                && !"ASM_1006".equals(item.getValueCode()) 
                && !"ASM_1007".equals(item.getValueCode()) 
                && !"ASM_1008".equals(item.getValueCode()));
        return list;
    }





    @Override
    public ResponseResult<Object> getEntrustmentForPrint(String reportNo) {
        // 使用私有方法获取已过滤的委托列表
        List<EntrustMainDTO> filteredList = getEntrustmentsForPrintFiltered(reportNo);

        // 关联任务完成时间
        TaskInfoDTO taskInfoDTO = new TaskInfoDTO();
        taskInfoDTO.setReportNo(reportNo);
        List<TaskInfoVO> taskInfoVOList = taskInfoMapper.getEntrustmentTaskInfo(taskInfoDTO);
        Map<String, TaskInfoVO> mapA = new HashMap<>();
        for (TaskInfoVO taskInfoVO : taskInfoVOList) {
            mapA.put(taskInfoVO.getOrderNo(), taskInfoVO);
        }
        for (EntrustMainDTO dto : filteredList) {
            TaskInfoVO vo = mapA.get(dto.getIdEntrust());
            if (vo != null && vo.getCompleteTime() != null) {
                dto.setCompleteTime(vo.getCompleteTime());
            }
        }

        // 增加公估公司
        TpaServerInfoListVO tpaServerInfoList = (TpaServerInfoListVO) investigateService.getServerInfoList().getData();
        if (tpaServerInfoList != null && tpaServerInfoList.getServerInfoList() != null) {
            List<ServerInfoVO> serverInfoList = tpaServerInfoList.getServerInfoList();
            Map<String, ServerInfoVO> mapB = new HashMap<>();
            for (ServerInfoVO serverInfoVO : serverInfoList) {
                mapB.put(serverInfoVO.getServerCode(), serverInfoVO);
            }
            for (EntrustMainDTO dto : filteredList) {
                ServerInfoVO vo = mapB.get(dto.getEntrustDptCode());
                if (vo != null) {
                    dto.setEntrustDptName(vo.getServerName());
                }
            }
        }

        // 增加序号
        List<EntrustMainDTO> list2 = new ArrayList<>();
        int i = 1;
        int j = 0;
        for (EntrustMainDTO EntrustMainDTO : filteredList) {
            EntrustMainDTO.setOrderNo(i + "-" + ++j);
            list2.add(EntrustMainDTO);
        }

        // 按完成时间倒序排列
        list2.sort((p1, p2) -> {
            if (p1.getCompleteTime() == null && p2.getCompleteTime() == null) {
                return 0;
            }
            if (p1.getCompleteTime() == null) {
                return 1;
            }
            if (p2.getCompleteTime() == null) {
                return -1;
            }
            return p2.getCompleteTime().compareTo(p1.getCompleteTime());
        });

        EntrustmentApiVo apiVo = new EntrustmentApiVo();
        apiVo.setEntrustMainList(list2);
        return ResponseResult.success(apiVo);
    }

    /**
     * 生成委托审批任务
     * @param user 当前用户
     */
    @Transactional(rollbackFor = Exception.class)
    public String generateEntrustmentAuditTask(EntrustMainDTO entrustMainDTO, UserInfoDTO user) {
        // 创建审批记录
        EntrustAuditDTO auditDTO = new EntrustAuditDTO();
        auditDTO.setIdEntrustAudit(UuidUtil.getUUID());
        auditDTO.setIdEntrustMain(entrustMainDTO.getIdEntrust());
        auditDTO.setReportNo(entrustMainDTO.getReportNo());
        auditDTO.setCaseTimes(entrustMainDTO.getCaseTimes());

        // 从查询结果中获取保单号和被保险人姓名
        Map<String, String> policyInfo = entrustmentMapper.getPolicyInfoByReportNo(entrustMainDTO.getReportNo());
        if (policyInfo != null) {
            auditDTO.setPolicyNo(policyInfo.get("policyNo"));
            auditDTO.setInsuredName(policyInfo.get("insuredName"));
        }

        // 复制委托信息中的数据
        auditDTO.setThirdPartyType(entrustMainDTO.getThirdPartyType());
        auditDTO.setEntrustDptCode(entrustMainDTO.getEntrustDptCode());
        auditDTO.setEntrustDptName(entrustMainDTO.getEntrustDptName());

        // 设置发起人信息
        auditDTO.setSubmitCode(user.getUserCode());
        auditDTO.setSubmitName(user.getUserName());

        // 设置审批人信息
        auditDTO.setAuditorCode(entrustMainDTO.getAuditorCode());
        auditDTO.setAuditorName(entrustMainDTO.getAuditorUmName());
        auditDTO.setAuditorDptCode(entrustMainDTO.getAuditorDpmCode());
        auditDTO.setAuditorDptName(entrustMainDTO.getAuditorDpmName());

        // 设置基础信息
        auditDTO.setAuditOpinion("1");// 1-待审核 2-不同意、3-同意
        auditDTO.setValidFlag("Y");
        auditDTO.setCreatedBy(user.getUserCode());
        auditDTO.setUpdatedBy(user.getUserCode());
        auditDTO.setSysCtime(new Date());
        auditDTO.setSysUtime(new Date());

        // 查询是否有在审批中的记录，没有就新增
        entrustmentAuditMapper.insertEntrustmentAudit(auditDTO);
        return auditDTO.getIdEntrustAudit();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitentrustAudit(EntrustAuditDTO entrustAuditDTO) throws GlobalBusinessException {
        // 更新审批记录
        EntrustAuditDTO entrustmentAudit = new EntrustAuditDTO();
        entrustmentAudit.setIdEntrustAudit(entrustAuditDTO.getIdEntrustAudit());
        entrustmentAudit.setIdEntrustMain(entrustAuditDTO.getIdEntrustMain());
        entrustmentAudit.setAuditOpinion(entrustAuditDTO.getAuditOpinion());
        entrustmentAudit.setRemark(entrustAuditDTO.getRemark());
        entrustmentAudit.setSysUtime(new Date());
        // 当前审批人
        UserInfoDTO user = WebServletContext.getUser();
        entrustmentAudit.setAuditorCode(user.getUserCode());
        entrustmentAudit.setAuditorName(user.getUserName());
        entrustmentAudit.setAuditorDptCode(WebServletContext.getDepartmentCode());
        entrustmentAudit.setAuditorDptName(departmentDefineMapper.queryDepartmentNameByDeptCode(WebServletContext.getDepartmentCode()));
        entrustmentAudit.setAuditTime(new Date());
        entrustmentAuditMapper.updateEntrustmentAudit(entrustmentAudit);

        // 更新委托表状态
        EntrustMainDTO entrustment = entrustmentMapper.selectById(entrustAuditDTO.getIdEntrustMain());
        if (entrustment != null) {
            if ("2".equals(entrustmentAudit.getAuditOpinion())) { // 不同意
                entrustment.setEntrustStatus("2");

                // 生成提醒任务
                generateRejectionNotice(entrustment, entrustment.getCreatedBy(), user.getUserName());
            } else if ("3".equals(entrustmentAudit.getAuditOpinion())) { // 同意
                entrustment.setEntrustStatus("3");

                //生成公估委托书
                PrintEntrustDTO printEntrustDTO = new PrintEntrustDTO();
                printEntrustDTO.setIdEntrust(entrustAuditDTO.getIdEntrustMain());
                printEntrustDTO.setPrintFlag(InvestigateConstants.PRINT_ENTRUSTMENT);
                String now = System.currentTimeMillis()+"";
                printCoreService.saveCommissionFileAsync(now,null,now,printEntrustDTO);
            }

            entrustment.setSysUtime(new Date());
            entrustment.setUpdatedBy(user.getUserCode());
            entrustmentMapper.updateEntrustment(entrustment);
        }
        // 关闭OC流程
        bpmService.completeTask_oc(entrustment.getReportNo(), entrustment.getCaseTimes(), BpmConstants.OC_ENTRUSTMENT_APPROVAL);

        // 操作记录
        operationRecordService.insertOperationRecordByLabour(entrustment.getReportNo(), BpmConstants.OC_ENTRUSTMENT_APPROVAL, "通过", entrustAuditDTO.getRemark());
    }

    /**
     * 生成审批退回提醒任务
     * @param entrustment 委托信息
     * @param audit 审批信息
     */
    private void generateRejectionNotice(EntrustMainDTO entrustment, String audit, String auditorName) {
        try {
            NoticesDTO noticesDTO = new NoticesDTO();
            noticesDTO.setReportNo(entrustment.getReportNo());
            noticesDTO.setNoticeClass(BpmConstants.NOTICE_CLASS_OC_BACK); // 审批退回
            noticesDTO.setNoticeSubClass(BpmConstants.NSC_ENTRUSTMENT); // 公估委托审批
            noticesDTO.setSourceSystem(BpmConstants.SOURCE_SYSTEM_OC);
            noticesDTO.setCaseTimes(entrustment.getCaseTimes());

            // 提醒内容："{报案号}第三方委托申请被{审批人}审批退回"
            String noticeContent = entrustment.getReportNo() + "第三方委托申请被" + auditorName + "审批退回";
            noticesDTO.setNoticeContent(noticeContent);

            // 发送给发起人
            noticeService.saveNotices(noticesDTO, audit);
        } catch (Exception e) {
            LogUtil.error("生成委托审批退回提醒失败", e);
        }
    }

    @Override
    public List<UserInfoDTO> getApprovalUsers(String reportNo, Integer caseTimes) throws GlobalBusinessException {
        try {
            InvestigateTaskDTO investigateTask = new InvestigateTaskDTO();
            investigateTask.setInvestigateDepartment(Constants.DEPARTMENT_CODE);
            TpaServerInfoListVO tpaServerInfoList = (TpaServerInfoListVO) investigateService.getServerInfoList().getData();
            List<ServerInfoVO> serverInfoList = tpaServerInfoList.getServerInfoList();
            List<String> serverCodeList = new ArrayList<>();
            if(ListUtils.isNotEmpty(serverInfoList)) {
                serverCodeList = serverInfoList.stream().map(ServerInfoVO::getServerCode).collect(Collectors.toList());
            }
            if (com.paic.ncbs.claim.common.util.StringUtils.isEmptyStr(investigateTask.getInvestigateDepartment()) || serverCodeList.contains(investigateTask.getInvestigateDepartment())){
                investigateTask.setInvestigateDepartment(WebServletContext.getDepartmentCode());
            }

            List<String> childCodeList = new ArrayList<String>();
            childCodeList.add(investigateTask.getInvestigateDepartment());
            List<String> parentCodeList = new ArrayList<String>();
            parentCodeList.add("775");
            childCodeList.addAll(departmentDefineMapper.getChildCodeList(parentCodeList));

            List<UserInfoDTO> userInfoDTO = new ArrayList<UserInfoDTO>();
            for(String investigateDepartment:childCodeList) {
                String departmentName = departmentDefineMapper.queryDepartmentNameByDeptCode(investigateDepartment);
                List<UserInfoDTO> departmentUserInfoDTO = taskPoolService.searchTaskDealUser(investigateDepartment, BpmConstants.OC_MAJOR_INVESTIGATE);
                departmentUserInfoDTO.forEach(e->{
                    e.setComCode(investigateDepartment);
                    e.setComName(departmentName);
                });
                userInfoDTO.addAll(departmentUserInfoDTO);
            }

            String departmentCode = entrustmentMapper.getDepartmentCodeByReportNo(reportNo,caseTimes);

            List<String> departmentCodes = new ArrayList<>();
            departmentCodes.add(departmentCode);
            departmentCodes.add(Constants.DEPARTMENT_CODE);

            // 筛选出ComCode在departmentCodes中的数据
            userInfoDTO = userInfoDTO.stream()
                    .filter(user -> departmentCodes.contains(user.getComCode()))
                    .collect(Collectors.toList());

            return userInfoDTO;
        } catch (Exception e) {
            throw new GlobalBusinessException("获取审批用户列表失败：" + e.getMessage());
        }
    }

    /**
     * 查询用于打印的委托信息（在Service层进行过滤）
     * @param reportNo 报案号
     * @return 符合打印条件的委托列表
     */
    private List<EntrustMainDTO> getEntrustmentsForPrintFiltered(String reportNo) {
        List<EntrustMainDTO> allEntrustments = entrustmentMapper.selectForPrint(reportNo);

        // 在Java代码中进行固定条件过滤：第三方类型为"公估"且审批状态为"同意"
        List<EntrustMainDTO> filteredList = allEntrustments.stream()
                .filter(e -> "01".equals(e.getThirdPartyType()) && "3".equals(e.getEntrustStatus()))
                .collect(Collectors.toList());
        
        // 转换thirdPartyType为thirdPartyTypeName
        filteredList.forEach(this::convertThirdPartyTypeName);
        
        return filteredList;
    }

    private void createNewEntrustment(EntrustMainDTO entrustMainDTO, String entrustId, String submitFlag, UserInfoDTO user) {
        entrustMainDTO.setIdEntrust(entrustId);
        entrustMainDTO.setEntrustStatus(submitFlag);
        entrustMainDTO.setPrintStatus("2");
        entrustMainDTO.setValidFlag("Y");
        entrustMainDTO.setCreatedBy(user.getUserCode());
        entrustMainDTO.setUpdatedBy(user.getUserCode());
        entrustMainDTO.setSysCtime(new Date());
        entrustMainDTO.setSysUtime(new Date());
    }

    /**
     * 转换thirdPartyType为thirdPartyTypeName/thirdPartyName
     * 转换第三方名称
     * @param object 委托主信息DTO或委托审批信息DTO
     */
    public void convertThirdPartyTypeName(Object object) {
        if (object == null) {
            return;
        }

        Map<String, String> thirdPartyTypeMap = new HashMap<>();
        thirdPartyTypeMap.put("01", "公估");
        thirdPartyTypeMap.put("02", "律师");
        thirdPartyTypeMap.put("03", "其他");

        String thirdPartyType = null;

        if (object instanceof EntrustMainDTO) {
            thirdPartyType = ((EntrustMainDTO) object).getThirdPartyType();
        } else if (object instanceof EntrustAuditDTO) {
            thirdPartyType = ((EntrustAuditDTO) object).getThirdPartyType();
        } else {
            return;
        }

        String thirdPartyName = thirdPartyTypeMap.get(thirdPartyType);
        if (thirdPartyName != null) {
            if (object instanceof EntrustMainDTO) {
                ((EntrustMainDTO) object).setThirdPartyName(thirdPartyName);
                EntrustMainDTO entrustMainDTO = (EntrustMainDTO) object;
                if ("01".equals(entrustMainDTO.getThirdPartyType())) {
                    entrustMainDTO.setEntrustDptName(entrustMainDTO.getEntrustDptName());
                } else {
                    entrustMainDTO.setEntrustDptName(entrustMainDTO.getEntrustName());
                }
            } else if (object instanceof EntrustAuditDTO) {
                ((EntrustAuditDTO) object).setThirdPartyName(thirdPartyName);
            }
        }
    }

    @Override
    public Integer getEntrustmentCount(String reportNo, Integer caseTimes) {
        return entrustmentMapper.getEntrustmentCount(reportNo, caseTimes);
    }

}