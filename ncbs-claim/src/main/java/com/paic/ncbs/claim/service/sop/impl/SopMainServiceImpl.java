package com.paic.ncbs.claim.service.sop.impl;

import com.paic.ncbs.claim.common.util.RapeCollectionUtils;
import com.paic.ncbs.claim.common.util.RapeStringUtils;
import com.paic.ncbs.claim.common.util.UuidUtil;
import com.paic.ncbs.claim.common.context.WebServletContext;
import com.paic.ncbs.claim.dao.entity.sop.ClmsSopMain;
import com.paic.ncbs.claim.dao.entity.sop.ClmsSopFile;
import com.paic.ncbs.claim.dao.entity.sop.ClmsSopConfig;
import com.paic.ncbs.claim.dao.mapper.sop.ClmsSopMainMapper;
import com.paic.ncbs.claim.dao.mapper.sop.ClmsSopFileMapper;
import com.paic.ncbs.claim.dao.mapper.sop.ClmsSopConfigMapper;
import org.apache.commons.lang3.StringUtils;
import com.paic.ncbs.claim.exception.GlobalBusinessException;
import com.paic.ncbs.claim.model.dto.sop.SopMainDTO;
import com.paic.ncbs.claim.model.vo.sop.SopMainVO;
import com.paic.ncbs.claim.model.vo.sop.SopQueryVO;
import com.paic.ncbs.claim.model.vo.sop.SopHistoryMainVO;
import com.paic.ncbs.claim.model.vo.sop.SopFileVO;
import com.paic.ncbs.claim.service.sop.SopFileService;
import com.paic.ncbs.claim.service.sop.SopMainService;
import com.paic.ncbs.claim.service.iobs.IOBSFileUploadService;
import com.paic.ncbs.um.model.dto.UserInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.Map;

/**
 * SOP管理Service实现类
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Slf4j
@Service
public class SopMainServiceImpl implements SopMainService {

    @Autowired
    private ClmsSopMainMapper clmsSopMainMapper;

    @Autowired
    private ClmsSopFileMapper clmsSopFileMapper;

    @Autowired
    private ClmsSopConfigMapper clmsSopConfigMapper;

    @Autowired
    private IOBSFileUploadService iobsFileUploadService;


    @Override
    public List<SopMainVO> getSopList(SopQueryVO queryVO) {
        log.info("查询SOP列表，查询条件：{}", queryVO);

        // 调用查询方法
        return clmsSopMainMapper.selectSopList(queryVO);
    }

    @Override
    public SopMainVO getSopDetail(String idSopMain) {
        log.info("获取SOP详情，idSopMain：{}", idSopMain);

        RapeStringUtils.checkIsEmpty(idSopMain, "SOP主键不能为空");

        SopMainVO sopMainVO = clmsSopMainMapper.selectSopDetailById(idSopMain);
        if (sopMainVO == null) {
            throw new GlobalBusinessException("SOP不存在");
        }

        // 查询历史版本信息
        List<SopHistoryMainVO> sopHistoryMainVOS = clmsSopMainMapper.selectListByBatchNo(sopMainVO.getSopName());
        sopMainVO.setSopHistoryMainVOS(sopHistoryMainVOS);

        // 查询文件列表
        List<ClmsSopFile> fileEntityList = clmsSopFileMapper.selectByIdSopMain(idSopMain);
        if (!RapeCollectionUtils.isEmpty(fileEntityList)) {
            List<SopFileVO> fileList = new ArrayList<>();
            for (ClmsSopFile fileEntity : fileEntityList) {
                SopFileVO fileVO = new SopFileVO();
                BeanUtils.copyProperties(fileEntity, fileVO);
                fileList.add(fileVO);
            }
            sopMainVO.setFileList(fileList);
        }

        log.info("获取SOP详情完成，SOP名称：{}", sopMainVO.getSopName());
        return sopMainVO;
    }

    @Override
    @Transactional
    public String saveOrUpdateSop(SopMainDTO sopMainDTO, HttpServletRequest request) {
        log.info("SOP操作，SOP名称：{}，操作类型：{}", sopMainDTO.getSopName(), sopMainDTO.getInitFlag());
        String initFlag = sopMainDTO.getInitFlag();

        // 校验必填字段
        RapeStringUtils.checkIsEmpty(sopMainDTO.getSopName(), "SOP名称不能为空");
        RapeStringUtils.checkIsEmpty(initFlag, "操作类型不能为空");

        UserInfoDTO userInfo = getUserInfo();
        LocalDateTime now = LocalDateTime.now();

        String idSopMain = sopMainDTO.getIdSopMain();
        boolean isUpdate = StringUtils.isNotEmpty(idSopMain);

        // 根据操作类型处理
        if ("01".equals(initFlag)) {
            // 暂存操作
            return handleSaveOperation(sopMainDTO, request, userInfo, now, isUpdate);
        } else if ("02".equals(initFlag)) {
            // 发布操作
            return handlePublishOperation(sopMainDTO, request, userInfo, now, isUpdate);
        } else if ("03".equals(initFlag)) {
            // 停用操作
            return handleDisableOperation(idSopMain, userInfo, now);
        } else if ("04".equals(initFlag)) {
            // 新增操作
            return handleAddOperation(sopMainDTO, request, userInfo, now);
        } else {
            throw new GlobalBusinessException("不支持的操作类型：" + initFlag);
        }
    }

    /**
     * 处理暂存操作
     */
    private String handleSaveOperation(SopMainDTO sopMainDTO, HttpServletRequest request, UserInfoDTO userInfo, LocalDateTime now, boolean isUpdate) {
        String idSopMain = sopMainDTO.getIdSopMain();
        String batchNo = sopMainDTO.getBatchNo();

        if (!isUpdate && StringUtils.isNotEmpty(batchNo)) {
            // 通过批次号查询最新一条数据
            ClmsSopMain existingEntity = clmsSopMainMapper.selectLatestByBatchNo(batchNo);
            if (existingEntity != null) {
                if ("01".equals(existingEntity.getStatus())) {
                    // 状态为01时更新原有数据
                    idSopMain = existingEntity.getIdSopMain();
                    sopMainDTO.setIdSopMain(idSopMain);
                    isUpdate = true;
                } else if ("02".equals(existingEntity.getStatus()) || "03".equals(existingEntity.getStatus())) {
                    // 状态为02或03时新增一条数据
                    sopMainDTO.setIdSopMain(UuidUtil.getUUID());
                    sopMainDTO.setBatchNo(batchNo);
                    sopMainDTO.setVersionNo(generateNewVersionNo(existingEntity.getVersionNo()));
                    sopMainDTO.setValidFlag("Y");
                    sopMainDTO.setPublisherCode(userInfo.getUserCode());
                    sopMainDTO.setPublisherName(userInfo.getUserName());
                    sopMainDTO.setPublishTime(now);
                    sopMainDTO.setEffectiveDate(now);
                    sopMainDTO.setCreatedBy(userInfo.getUserCode());
                    sopMainDTO.setSysCtime(now);
                }
            } else {
                // 没有历史数据，新增
                idSopMain = UuidUtil.getUUID();
                sopMainDTO.setIdSopMain(idSopMain);
                sopMainDTO.setBatchNo(UuidUtil.getUUID());
                sopMainDTO.setVersionNo("1.0");
                sopMainDTO.setValidFlag("Y");
                sopMainDTO.setPublisherCode(userInfo.getUserCode());
                sopMainDTO.setPublisherName(userInfo.getUserName());
                sopMainDTO.setPublishTime(now);
                sopMainDTO.setEffectiveDate(now);
                sopMainDTO.setCreatedBy(userInfo.getUserCode());
                sopMainDTO.setSysCtime(now);
            }
        } else if (!isUpdate) {
            // 没有批次号的情况，按原逻辑处理
            idSopMain = UuidUtil.getUUID();
            sopMainDTO.setIdSopMain(idSopMain);
            sopMainDTO.setBatchNo(UuidUtil.getUUID());
            sopMainDTO.setVersionNo("1.0");
            sopMainDTO.setValidFlag("Y");
            sopMainDTO.setPublisherCode(userInfo.getUserCode());
            sopMainDTO.setPublisherName(userInfo.getUserName());
            sopMainDTO.setPublishTime(now);
            sopMainDTO.setEffectiveDate(now);
            sopMainDTO.setCreatedBy(userInfo.getUserCode());
            sopMainDTO.setSysCtime(now);
        }

        sopMainDTO.setStatus("01"); // 暂存状态
        sopMainDTO.setUpdatedBy(userInfo.getUserCode());
        sopMainDTO.setSysUtime(now);

        // 转换为实体类
        ClmsSopMain entity = new ClmsSopMain();
        BeanUtils.copyProperties(sopMainDTO, entity);

        if (isUpdate) {
            clmsSopMainMapper.updateByPrimaryKeySelective(entity);
        } else {
            clmsSopMainMapper.insertSelective(entity);
        }

        // 删除原有关联数据并重新录入
        clmsSopConfigMapper.deleteByIdSopMain(idSopMain);
        clmsSopFileMapper.deleteByIdSopMain(idSopMain);

        // 保存配置关联数据
        saveConfigData(idSopMain, sopMainDTO, userInfo.getUserCode(), now);

        // 保存文件数据
        saveFileData(idSopMain, sopMainDTO, request, userInfo.getUserCode(), now);

        log.info("暂存SOP完成，idSopMain：{}", idSopMain);
        return idSopMain;
    }

    /**
     * 处理发布操作
     */
    private String handlePublishOperation(SopMainDTO sopMainDTO, HttpServletRequest request, UserInfoDTO userInfo, LocalDateTime now, boolean isUpdate) {
        // 先执行暂存操作
        String idSopMain = handleSaveOperation(sopMainDTO, request, userInfo, now, isUpdate);

        // 查询当前SOP信息
        ClmsSopMain currentEntity = clmsSopMainMapper.selectByPrimaryKey(idSopMain);
        String batchNo = currentEntity.getBatchNo();

        // 根据SOP名称查询最新数据，判断是否需要更新版本号
        ClmsSopMain latestEntity = clmsSopMainMapper.selectBySopName(currentEntity.getSopName());
        String newVersionNo = currentEntity.getVersionNo();

        if (latestEntity != null && ("02".equals(latestEntity.getStatus()) || "03".equals(latestEntity.getStatus()))) {
            // 如果最新数据状态为02或03，需要在原版本号基础上加0.1
            newVersionNo = generateNewVersionNo(latestEntity.getVersionNo());

            // 使同批次号的其他数据失效
            invalidateOtherVersionsByBatchNo(batchNo, idSopMain, userInfo.getUserCode(), now);
        }

        // 更新状态为发布
        ClmsSopMain entity = new ClmsSopMain();
        entity.setIdSopMain(idSopMain);
        entity.setVersionNo(newVersionNo);
        entity.setStatus("02");
        entity.setPublisherCode(userInfo.getUserCode());
        entity.setPublisherName(userInfo.getUserName());
        entity.setPublishTime(now);
        entity.setEffectiveDate(now);
        entity.setUpdatedBy(userInfo.getUserCode());
        entity.setSysUtime(now);

        clmsSopMainMapper.updateByPrimaryKeySelective(entity);

        log.info("发布SOP完成，idSopMain：{}，版本号：{}", idSopMain, newVersionNo);
        return idSopMain;
    }

    /**
     * 处理停用操作
     */
    private String handleDisableOperation(String idSopMain, UserInfoDTO userInfo, LocalDateTime now) {
        RapeStringUtils.checkIsEmpty(idSopMain, "SOP主键不能为空");

        ClmsSopMain entity = clmsSopMainMapper.selectByPrimaryKey(idSopMain);
        if (entity == null) {
            throw new GlobalBusinessException("SOP不存在");
        }

        if (!"02".equals(entity.getStatus())) {
            throw new GlobalBusinessException("只有有效状态的SOP才能停用");
        }

        // 更新状态为停用
        entity.setStatus("03");
        entity.setInvalidDate(now);
        entity.setUpdatedBy(userInfo.getUserCode());
        entity.setSysUtime(now);

        clmsSopMainMapper.updateByPrimaryKeySelective(entity);

        log.info("停用SOP完成，idSopMain：{}", idSopMain);
        return idSopMain;
    }

    /**
     * 处理新增操作
     */
    private String handleAddOperation(SopMainDTO sopMainDTO, HttpServletRequest request, UserInfoDTO userInfo, LocalDateTime now) {
        // 新增操作，默认初始版本号为1.0
        String idSopMain = UuidUtil.getUUID();
        sopMainDTO.setIdSopMain(idSopMain);
        sopMainDTO.setVersionNo("1.0");
        sopMainDTO.setStatus("01"); // 暂存状态
        sopMainDTO.setValidFlag("Y");
        sopMainDTO.setCreatedBy(userInfo.getUserCode());
        sopMainDTO.setSysCtime(now);
        sopMainDTO.setUpdatedBy(userInfo.getUserCode());
        sopMainDTO.setSysUtime(now);

        // 转换为实体类
        ClmsSopMain entity = new ClmsSopMain();
        BeanUtils.copyProperties(sopMainDTO, entity);
        clmsSopMainMapper.insertSelective(entity);

        // 保存配置关联数据
        saveConfigData(idSopMain, sopMainDTO, userInfo.getUserCode(), now);

        // 保存文件数据
        saveFileData(idSopMain, sopMainDTO, request, userInfo.getUserCode(), now);

        log.info("新增SOP完成，idSopMain：{}", idSopMain);
        return idSopMain;
    }

    /**
     * 生成新版本号（在原版本号基础上加0.1）
     */
    private String generateNewVersionNo(String currentVersionNo) {
        try {
            double version = Double.parseDouble(currentVersionNo);
            return String.format("%.1f", version + 0.1);
        } catch (NumberFormatException e) {
            log.warn("版本号格式异常，使用默认版本号：{}", currentVersionNo);
            return "1.0";
        }
    }

    /**
     * 使同批次号的其他数据失效
     */
    private void invalidateOtherVersionsByBatchNo(String batchNo, String excludeId, String updatedBy, LocalDateTime now) {
        if (StringUtils.isEmpty(batchNo)) {
            return;
        }

        // 更新同批次号的其他数据失效日期
        clmsSopMainMapper.updateInvalidDateByBatchNo(batchNo, excludeId, updatedBy, now);
        log.info("批次号{}的其他版本已失效，排除ID：{}", batchNo, excludeId);
    }

    @Override
    public List<SopMainVO> matchSopRules(String productCode, String groupCode, String planCode, String taskDefinitionBpmKey) {
        log.info("匹配SOP规则，productCode：{}，groupCode：{}，planCode：{}，taskDefinitionBpmKey：{}",
                productCode, groupCode, planCode, taskDefinitionBpmKey);

        List<SopMainVO> list = clmsSopMainMapper.selectMatchingSopRules(productCode, groupCode, planCode, taskDefinitionBpmKey);
        
        log.info("匹配SOP规则完成，共匹配到{}条SOP", list.size());
        return list;
    }

    @Override
    public String getNextVersionNo(String sopName) {
        String maxVersion = clmsSopMainMapper.selectMaxVersionBySopName(sopName);
        if (org.apache.commons.lang3.StringUtils.isEmpty((CharSequence) maxVersion)) {
            return "1.0";
        }
        
        try {
            String[] parts = maxVersion.split("\\.");
            int major = Integer.parseInt(parts[0]);
            int minor = parts.length > 1 ? Integer.parseInt(parts[1]) : 0;
            minor++;
            return major + "." + minor;
        } catch (Exception e) {
            log.warn("解析版本号失败，使用默认版本号，maxVersion：{}", maxVersion, e);
            return "1.0";
        }
    }

    @Override
    public boolean checkSopNameExists(String sopName) {
        int count = clmsSopMainMapper.countBySopName(sopName, null);
        return count > 0;
    }

    @Override
    public List<Object> getPlanInfoList() {
        return clmsSopMainMapper.selectPlanInfoList();
    }

    @Override
    public List<SopMainVO> matchSopRulesByCase(String reportNo, Integer caseTimes, String taskBpmKey) {
        return clmsSopMainMapper.selectMatchingSopRulesByCase(reportNo, caseTimes, taskBpmKey);
    }

    /**
     * 保存文件数据
     */
    private void saveFileData(String idSopMain, SopMainDTO sopMainDTO, HttpServletRequest request, String userId, LocalDateTime now) {
        try {
            if (request instanceof MultipartHttpServletRequest) {
                MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
                Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();

                if (!RapeCollectionUtils.isEmpty(fileMap)) {
                    List<ClmsSopFile> fileList = new ArrayList<>();

                    for (Map.Entry<String, MultipartFile> entry : fileMap.entrySet()) {
                        MultipartFile file = entry.getValue();
                        if (file != null && !file.isEmpty()) {
                            String fileName = file.getOriginalFilename();
                            String fileFormat = fileName.substring(fileName.lastIndexOf(".") + 1);

                            // 上传文件到IOBS
                            String fileId = iobsFileUploadService.uploadFileToFilePlatform(fileName, file.getBytes());
                            String fileUrl = iobsFileUploadService.getPerpetualDownloadUrl(fileId, fileName);

                            // 保存文件信息
                            ClmsSopFile entity = new ClmsSopFile();
                            entity.setIdSopFile(UuidUtil.getUUID());
                            entity.setIdSopMain(idSopMain);
                            entity.setFileId(fileId);
                            entity.setFileUrl(fileUrl);
                            entity.setFileName(fileName);
                            entity.setFileFormat(fileFormat);
                            entity.setFileType(StringUtils.isNotEmpty(sopMainDTO.getFileType()) ? sopMainDTO.getFileType() : "SOP");
                            entity.setUploadTime(now);
                            entity.setValidFlag("Y");
                            entity.setCreatedBy(userId);
                            entity.setSysCtime(now);
                            entity.setUpdatedBy(userId);
                            entity.setSysUtime(now);

                            fileList.add(entity);
                        }
                    }

                    if (!RapeCollectionUtils.isEmpty(fileList)) {
                        clmsSopFileMapper.batchInsert(fileList);
                    }
                }
            }
        } catch (Exception e) {
            log.error("保存文件数据失败", e);
            throw new GlobalBusinessException("文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 保存配置数据
     */
    private void saveConfigData(String idSopMain, SopMainDTO sopMainDTO, String userId, LocalDateTime now) {
        // 获取产品、方案、险种、环节列表
        List<String> productCodes = sopMainDTO.getProductCodes();
        List<String> groupCodes = sopMainDTO.getGroupCodes();
        List<String> planCodes = sopMainDTO.getPlanCodes();
        List<String> taskBpmKeys = sopMainDTO.getTaskBpmKeys();

        List<ClmsSopConfig> configList = generateConfigList(idSopMain, productCodes, groupCodes, planCodes, taskBpmKeys, userId, now);

        if (!RapeCollectionUtils.isEmpty(configList)) {
            clmsSopConfigMapper.batchInsert(configList);
        }
    }

    /**
     * 生成配置列表
     */
    private List<ClmsSopConfig> generateConfigList(String idSopMain, List<String> productCodes, List<String> groupCodes, List<String> planCodes, List<String> taskBpmKeys, String userId, LocalDateTime now) {
        List<ClmsSopConfig> configList = new ArrayList<>();

        // 处理空值，设置为PUB
        if (RapeCollectionUtils.isEmpty(planCodes)) {
            planCodes = Arrays.asList("PUB");
        }
        if (RapeCollectionUtils.isEmpty(taskBpmKeys)) {
            taskBpmKeys = Arrays.asList("PUB");
        }

        List<ClmsSopConfig> productGroupConfigs = new ArrayList<>();

        // 根据不同的参数组合情况生成配置
        buildValidProductGroupPairs(idSopMain,productCodes, groupCodes, planCodes, taskBpmKeys, userId, now, productGroupConfigs);

        configList.addAll(productGroupConfigs);
        return configList;
    }
    
    /**
     * 构建有效的product-group配对列表
     */
    private List<ProductGroupPair> buildValidProductGroupPairs(String idSopMain,List<String> productCodes, List<String> groupCodes,
                                                               List<String> planCodes, List<String> taskBpmKeys,
                                                               String userId, LocalDateTime now, List<ClmsSopConfig> productGroupConfigs) {
        List<ProductGroupPair> validPairs = new ArrayList<>();
        
        // 当productCodes、groupCodes同时有值时，查询关联关系
        if (!RapeCollectionUtils.isEmpty(productCodes) && !RapeCollectionUtils.isEmpty(groupCodes)) {
            List<Map<String, Object>> productGroupAssociations = clmsSopMainMapper.selectProductGroupAssociations();
            
            if (!RapeCollectionUtils.isEmpty(productGroupAssociations)) {
                Set<String> matchedProducts = new HashSet<>();
                Set<String> matchedGroups = new HashSet<>();
                
                // 查找能够匹配上productCodes和groupCodes的关联关系
                for (Map<String, Object> association : productGroupAssociations) {
                    String associatedProductCode = (String) association.get("productCode");
                    String associatedGroupCode = (String) association.get("groupCode");
                    
                    if (productCodes.contains(associatedProductCode) && groupCodes.contains(associatedGroupCode)) {
                        validPairs.add(new ProductGroupPair(associatedProductCode, associatedGroupCode));
                        matchedProducts.add(associatedProductCode);
                        matchedGroups.add(associatedGroupCode);
                    }
                }
                
                // 如果productCodes中有product没有匹配上groupCodes中的group，则用PUB填充group
                for (String productCode : productCodes) {
                    if (!matchedProducts.contains(productCode)) {
                        validPairs.add(new ProductGroupPair(productCode, "PUB"));
                    }
                }
            }
        }
        // 当productCodes有值、groupCodes没有值时，将groupCodes设置成PUB
        else if (!RapeCollectionUtils.isEmpty(productCodes) && RapeCollectionUtils.isEmpty(groupCodes)) {
            for (String productCode : productCodes) {
                validPairs.add(new ProductGroupPair(productCode, "PUB"));
            }
        }
        // 当groupCodes有值、productCodes没有值时，将productCodes设置成PUB
        else if (RapeCollectionUtils.isEmpty(productCodes) && !RapeCollectionUtils.isEmpty(groupCodes)) {
            for (String groupCode : groupCodes) {
                validPairs.add(new ProductGroupPair("PUB", groupCode));
            }
        }
        // 当productCodes、groupCodes同时没有值时，都设置成PUB
        else {
            validPairs.add(new ProductGroupPair("PUB", "PUB"));
        }

        for (ProductGroupPair pair : validPairs) {
            for (String planCode : planCodes) {
                for (String taskBpmKey : taskBpmKeys) {
                    productGroupConfigs.add(createConfigEntity(idSopMain, pair.productCode, pair.groupCode, planCode, taskBpmKey, userId, now));
                }
            }
        }
        return validPairs;
    }
    
    /**
     * 产品-方案配对内部类
     */
    private static class ProductGroupPair {
        String productCode;
        String groupCode;
        
        public ProductGroupPair(String productCode, String groupCode) {
            this.productCode = productCode;
            this.groupCode = groupCode;
        }
    }

    /**
     * 创建配置实体
     */
    private ClmsSopConfig createConfigEntity(String idSopMain, String productCode, String groupCode, String planCode, String taskBpmKey, String userId, LocalDateTime now) {
        ClmsSopConfig config = new ClmsSopConfig();
        config.setIdSopConfig(UuidUtil.getUUID());
        config.setIdSopMain(idSopMain);
        config.setProductCode(productCode);
        config.setGroupCode(groupCode);
        config.setPlanCode(planCode);
        config.setTaskBpmKey(taskBpmKey);
        config.setCreatedBy(userId);
        config.setSysCtime(now);
        config.setUpdatedBy(userId);
        config.setSysUtime(now);
        return config;
    }

    /**
     * 获取当前用户信息
     */
    private UserInfoDTO getUserInfo() {
        return WebServletContext.getUser();
    }

}
