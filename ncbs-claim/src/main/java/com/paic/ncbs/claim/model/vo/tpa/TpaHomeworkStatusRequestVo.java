package com.paic.ncbs.claim.model.vo.tpa;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class TpaHomeworkStatusRequestVo {
    // 出单机构
    private String departmentCode;
    private List<String> departmentCodes;
    // 产品代码
    private String productCode;
    // TPA
    private String submitTpa;
    // 作业人
    private String submitter;
    // 日期统计(当日：day 当周：week 当月：month 上月：lastMonth)
    private String statisticsDate;
    //调查机构
    private String initiateDepartment ;


}
