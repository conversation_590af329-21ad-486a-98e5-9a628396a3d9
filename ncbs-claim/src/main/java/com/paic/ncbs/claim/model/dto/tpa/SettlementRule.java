package com.paic.ncbs.claim.model.dto.tpa;

import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class SettlementRule {
    private Long id; // 规则ID
    private String stage; // 环节名称
    private Boolean isReopened; // 是否重开
    private String submitType; // 提交人
    private String errorTypeIncluded;// 包含的差错类型（逗号分隔）
    private String reopenedTypeIncluded; // 包含的重开类型（逗号分隔）
//    private String errorTypeExcluded;// 排除的差错类型（逗号分隔）
    private Boolean tpaConsistent; // TPA是否一致
    private Integer priority; // 优先级
    private String remark; // 备注 // 辅助方法：解析包含的差错类型为列表
    public List<String> getErrorTypeIncludedList() {
        return parseStringToList(errorTypeIncluded);
    } // 辅助方法：解析排除的差错类型为列表
    public List<String> getReopenedTypeIncludedList() {
        return parseStringToList(reopenedTypeIncluded);
    }
//    public List<String> getErrorTypeExcludedList() {
//        return parseStringToList(errorTypeExcluded);
//    }
    // 工具方法：将逗号分隔的字符串转为列表
    private List<String> parseStringToList(String str) {
//        if (str == null || str.trim().isEmpty()) {
//            return List.of();
//        }
        return Arrays.stream(str.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }
}
