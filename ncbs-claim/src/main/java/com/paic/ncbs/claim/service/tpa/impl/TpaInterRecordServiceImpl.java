package com.paic.ncbs.claim.service.tpa.impl;

import com.paic.ncbs.claim.dao.entity.tpa.ClmsTpaInterRecord;
import com.paic.ncbs.claim.dao.mapper.tpa.ClmsTpaInterRecordMapper;
import com.paic.ncbs.claim.dao.mapper.tpa.ClmsTpaSettleInfoMapper;
import com.paic.ncbs.claim.service.tpa.TpaInterRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 理赔与结算平台交互记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Service
public class TpaInterRecordServiceImpl extends ServiceImpl<ClmsTpaInterRecordMapper, ClmsTpaInterRecord> implements TpaInterRecordService {
    @Autowired
    private ClmsTpaSettleInfoMapper clmsTpaSettleInfoMapper;

    @Override
    public void saveTpaInterRecord(ClmsTpaInterRecord clmsTpaInterRecord) {

    }
}
