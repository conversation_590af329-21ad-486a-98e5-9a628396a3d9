package com.paic.ncbs.claim.model.vo.tpa;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TpaHomeworkStatusVo {
    // 接报案量
    private String reportCaseNumber;
    // 结案数量
    private String caseNumber;
    // 重开案件数
    private String reopenNumber;
    // 案均结案金额
    private String averagePay;
    // 案均报案-立案周期
    private String filingCycle;
    // 案均报案-结案周期
    private String caseCycle;
    // 结案率
    private String caseRate;
    // TPA费用金额
    private String tpaPay;
    // 差错件数
    private String errorsNumber;
}
